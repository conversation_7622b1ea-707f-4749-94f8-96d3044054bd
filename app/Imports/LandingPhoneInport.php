<?php

namespace App\Imports;

use Base\Models\TrackingUtmLandingDataPhone;
use Base\Models\TrackingViewLandingData;
use Base\Models\TrackingViewLandingDataPhone;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Maatwebsite\Excel\Row;
use Maatwebsite\Excel\Concerns\OnEachRow;

class LandingPhoneInport implements OnEachRow, WithStartRow
{
    public function onRow(Row $row)
    {
        $row = $row->toArray();
        $name = $row[0];
        $phone = $this->checkPhone($row[1]);
        $email = $row[2];
        $source = 'mepuzz';
        $medium = 'Form';
        $device = $this->getDevice($row[3]);
        $class = $this->getClass($row[3]);
        $content = '';
        $landingId = 'from_mepuzz';
        $data = [
            'user_id' => null,
            'landing_id' => $landingId,
            'name' => $name,
            'phone' => $phone,
            'email' => $email,
            'class_level' => $class,
            'fb_url' => '',
            'utm_source' => $source,
            'utm_medium' => $medium,
            'utm_content' => $content,
            'utm_term' => $device,
            'utm_campaign' => $class
        ];
        TrackingViewLandingData::create($data);
        $dataExist = TrackingViewLandingDataPhone::where('phone', $phone)->first();
        if (!$dataExist) {
            TrackingViewLandingDataPhone::create($data);
        } else {
            TrackingViewLandingDataPhone::where('phone', $phone)->update(
                [
                    'phone' => $data['phone'],
                    'utm_source' => $data['utm_source'],
                    'utm_medium' => $data['utm_medium'],
                    'utm_campaign' => $data['utm_campaign'],
                    'utm_term' => $data['utm_term']
                ]);
        }
        $dataExistUtm = TrackingUtmLandingDataPhone::where('phone', $phone)->first();
        if (!$dataExistUtm) {
            TrackingUtmLandingDataPhone::create($data);
        } else {
            TrackingUtmLandingDataPhone::where('phone', $phone)->update(
                [
                    'phone' => $data['phone'],
                    'utm_source' => $data['utm_source'],
                    'utm_medium' => $data['utm_medium'],
                    'utm_campaign' => $data['utm_campaign'],
                    'utm_term' => $data['utm_term']
                ]
            );
        }
    }

    /**
     * @return int
     */
    public function startRow(): int
    {
        return 2;
    }

    private function getDevice($string)
    {
        return explode(' ', $string)[1];
    }

    private function getClass($string)
    {
        $class = explode(' ', $string)[2] . ' ' . explode(' ', $string)[3];
        if (!empty(explode(' ', $string)[4])) {
            $class = $class . ' ' . explode(' ', $string)[4];
        }
        return $class;
    }

    private function checkPhone($phone) {
        if (strlen($phone) < 10) {
            $phone = str_repeat('0', (10 - strlen($phone))) . $phone;
        }

        return $phone;
    }
}
