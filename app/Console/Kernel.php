<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        //
    ];

    /**
     * Define the application's command schedule.
     *
     * @param \Illuminate\Console\Scheduling\Schedule $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        if (config('app.page_type') == 'khoahoc') {
            $schedule->command('course:publish')->hourly();
            // $schedule->command('reset:phone')->hourly();
            $schedule->command('share_phone_has_new')->cron('0 */4 * * *'); // every 4 hours
            $schedule->command('reset:count2weekQuestion')->dailyAt('00:00');
            $schedule->command('reset:count2weekCourseCurriculumItems')->dailyAt('01:00');
            $schedule->command('elastic:import-question')->dailyAt('01:15');
            $schedule->command('elastic:import-exam')->dailyAt('01:45');
            $schedule->command('authentication-log:purge')->dailyAt('02:00');
            $schedule->command('order-packages:purge')->dailyAt('02:30');
            $schedule->command('reset:count2weekCourse')->dailyAt('03:00');
            $schedule->command('elastic:import-admission')->weeklyOn(0, '4:00'); // chạy 4h sáng chủ nhật hàng tuần
            // $schedule->command('auto:increaseClass')->cron('0 0 1 04 *'); // 1/4 every years
        }
        // if (config('app.page_type') == 'video') {
        //     $schedule->command('video:render')->everyTenMinutes();
        // }

        // $schedule->command('db:backup')->hourly();
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
