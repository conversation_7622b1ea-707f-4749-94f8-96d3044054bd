<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Course\Models\Course;
use Course\Models\LectureTest;
use Course\Models\CourseCurriculumItems;
use MultipleChoices\Models\Question;
use MultipleChoices\Models\CurriculumQuestion;
use Base\Models\TrackingViewLandingData;
use Cart\Models\OrderPackage;
use Category\Models\Category;
use Admission\Models\AdmissionPost;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class HandleData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'handle-data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        // $courses = Course::where('type', 'normal')
        //     ->where('status', 'disable')
        //     ->where('created_at', '<', '2023-01-01')
        //     ->orderBy('id', 'DESC')
        //     ->get();

        // 229,228,227,226,225,224,223
        // 222
        // 221
        // 220
        // 218
        // 217
        // 216
        // 215
        // 214
        // 213
        // 212
        // 211
        // 208
        // 207
        // 202
        // 201
        // 200
        // 199
        // 198
        // 197
        // 196
        // 195
        // 192
        // 191
        // 190
        // 189
        // 188
        // 187
        // 186
        // 185
        // 184
        // 183
        // 182
        // 181
        // 180
        // 179
        // 177
        // 176
        // 173
        // 172
        // 19
        // 18
        // 17
        // 16
        // 15
        // 14
        // 13
        // 10
        // 9
        // 6
        // 5
        // 4
        // 2
        // 1
        // những id trên là chưa bê sang ổ backup vì ổ backup đã full
        // lấy danh sách các file video của các khóa cũ bị disable
        // $curriculums = CourseCurriculumItems::whereIn('course_id', [266,265,242,239,232,231,230])
        //     ->with(['getMedia' => function($query) {
        //         $query->where('type', 'video/mp4');
        //     }])
        //     ->get();

        // foreach ($curriculums as $curriculum) {
        //     foreach ($curriculum->getMedia as $media) {
        //         $url = $media->raw_url;
        //         $prefix = '';

        //         if (strpos($url, 'upload2/') !== false) {
        //             $prefix = '/data2';
        //         } else {
        //             $prefix = '/data1';
        //         }

        //         $info = pathinfo($url);
        //         $dirname  = $info['dirname'];
        //         $basename = $info['filename'];

        //         $originalPath = rtrim($prefix, '/') . '/' . ltrim($url, '/');
        //         $resizedPath  = rtrim($prefix, '/') . '/' . ltrim($dirname, '/') . '/' . $basename . '_x480.mp4';

        //         print_r($originalPath);
        //         print_r("\n");
        //         print_r($resizedPath);
        //         print_r("\n");
        //     }
        // }

        print_r('End!');
        print_r("\n");
    }
}
