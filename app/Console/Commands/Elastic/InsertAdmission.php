<?php

namespace App\Console\Commands\Elastic;

use Illuminate\Console\Command;
use Admission\Models\Admission;
use Admission\Models\AdmissionPost;
use Base\Services\ElasticSearchService;
use Illuminate\Support\Arr;
use Throwable;

class InsertAdmission extends Command
{
    /**
     * @var string
     */
    protected $signature = 'elastic:import-admission';

    /**
     * @var Client
     */
    private $client;
    private $indexName;

    public function __construct()
    {
        $this->client = app()->make(ElasticSearchService::class)->connection();
        $this->indexName = config('elasticsearch.indexName.kh_admission');

        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        if (!$this->client->indices()->exists([
            'index' => $this->indexName,
        ])) {
            $this->createNewIndex();
        }

        $this->insertDataIntoIndex();
    }

    /**
     * @see https://github.com/elastic/elasticsearch-php
     * @see https://github.com/duydo/elasticsearch-analysis-vietnamese
     * @see https://www.elastic.co/guide/en/elasticsearch/reference/master/analyzer.html
     * @see https://www.elastic.co/guide/en/elasticsearch/reference/master/search-analyzer.html
     *
     */
    private function createNewIndex()
    {
        try {
            $this->client->indices()->create([
                'index' => $this->indexName,
                'body' => [
                    'settings' => [
                        'number_of_shards' => 2, // https://www.elastic.co/guide/en/elasticsearch/reference/current/index-modules.html
                        'number_of_replicas' => 1,
                        'max_result_window' => 1000000,
                        'analysis' => [
                            'normalizer' => [ // https://www.elastic.co/guide/en/elasticsearch/reference/current/normalizer.html
                                'keyword_lowercase' => [
                                    'type' => 'custom',
                                    'char_filter' => [],
                                    'filter' => [
                                        'lowercase',
                                        'asciifolding'
                                    ],
                                ],
                            ],
                            'analyzer' => [ // https://www.elastic.co/guide/en/elasticsearch/reference/current/analysis-custom-analyzer.html
                                'vi_indexing_analyzer' => [
                                    'tokenizer' => 'vi_tokenizer',
                                    'filter' => [
                                        'vi_stop',
                                        'word_multiplexer',
                                    ],
                                ],
                                'vi_searching_analyzer' => [
                                    'tokenizer' => 'vi_tokenizer',
                                    'filter' => [
                                        'vi_stop',
                                        'synonym_search',
                                    ],
                                ],
                                'vi_string_analyzer' => [
                                    'tokenizer' => 'vi_tokenizer',
                                    'filter' => [
                                        'lowercase',
                                        'synonym_search',
                                        'word_multiplexer',
                                    ],
                                ],
                            ],
                            'filter' => [
                                'word_multiplexer' => [
                                    'type' => 'multiplexer',
                                    'filters' => [
                                        'icu_folding',
                                        'word_delimiter',
                                    ],
                                ],
                                'synonym_search' => [ // https://www.elastic.co/guide/en/elasticsearch/reference/current/analysis-synonym-tokenfilter.html
                                    'type' => 'synonym',
                                    'synonyms' => [
                                        'tp, thành phố',
                                        'hcm, hồ chí minh',
                                        'hn, hà nội',
                                        'dh, đh, đại học',
                                        'cd, cđ, cao đẳng',
                                        'thpt, trung học phổ thông',
                                        'gd, bgd, giáo dục, bộ giáo dục',
                                        'đgtd, đánh giá năng lực',
                                        'đt, đào tạo',
                                    ],
                                ],
                            ],
                        ],
                    ],
                    'mappings' => [ // https://www.elastic.co/guide/en/elasticsearch/reference/current/mapping-types.html
                        'properties' => [
                            '@timestamp' => [
                                'type' => 'date',
                            ],
                            'created_at' => [
                                'type' => 'date',
                            ],
                            'updated_at' => [
                                'type' => 'date',
                            ],
                            "code" => [
                                'type' => 'text',
                                'fields' => [ // https://www.elastic.co/guide/en/elasticsearch/reference/current/multi-fields.html
                                    'keyword' => [
                                        'type' => 'keyword',
                                        'normalizer'=> 'keyword_lowercase',
                                        'ignore_above' => 500,
                                    ],
                                ],
                                'analyzer' => 'vi_indexing_analyzer',
                                'search_analyzer' => 'vi_searching_analyzer',
                                'search_quote_analyzer' => 'vi_string_analyzer',
                            ],
                            'name' => [
                                'type' => 'text',
                                'fields' => [ // https://www.elastic.co/guide/en/elasticsearch/reference/current/multi-fields.html
                                    'keyword' => [
                                        'type' => 'keyword',
                                        'normalizer'=> 'keyword_lowercase',
                                        'ignore_above' => 500,
                                    ],
                                ],
                                'analyzer' => 'vi_indexing_analyzer',
                                'search_analyzer' => 'vi_searching_analyzer',
                                'search_quote_analyzer' => 'vi_string_analyzer',
                            ],
                        ],
                    ],
                ]
            ]);
        } catch (Throwable $exception) {
            $this->output->writeln(
                sprintf(
                    '<error>Error creating index %s, exception message: %s.</error>',
                    $this->indexName,
                    $exception->getMessage()
                )
            );

            exit();
        }
    }

    private function insertDataIntoIndex()
    {
        try {
            $countAdmission = Admission::count();
            $perPage = 2000;
            $totalPage = ceil($countAdmission / $perPage);

            for ($i = 0; $i < $totalPage; $i++) {
                $esDatas = collect($this->getElasticsearchData($perPage * $i, $perPage));
                $admissions = Admission::select([
                        'id',
                        'name',
                        'slug',
                        'code',
                        'created_at',
                        'updated_at',
                    ])
                    ->skip($perPage * $i)
                    ->take($perPage)
                    ->get()
                    ->toArray();

                for ($j = 0; $j < count($admissions); $j++) {
                    $admissions[$j] = $this->transformDataAdmission($admissions[$j]);
                    $subData = [];

                    foreach (AdmissionPost::postTypes() as $postType) {
                        $lastPosst = AdmissionPost::select([
                                'id',
                                'name',
                                'slug',
                                'admission_id',
                                'type',
                                'created_at',
                                'updated_at',
                            ])
                            ->where('admission_id', $admissions[$j]['id'])
                            ->where('type', $postType['value'])
                            ->orderByDesc('id')
                            ->first();

                        if ($lastPosst) {
                            $subData[] = $this->transformDataAdmissionPost($admissions[$j]['code'], $lastPosst->toArray());
                        }
                    }

                    $admissions[$j]['posts'] = $subData;

                    if ($esDatas->contains('_id', $admissions[$j]['id'])) {
                        $this->client->update([
                            'index' => $this->indexName,
                            'id'    => $admissions[$j]['id'],
                            'body'  => [
                                'doc' => $admissions[$j],
                            ]
                        ]);
                        echo 'Update id: ' . $admissions[$j]['id'] . "\n";
                    } else {
                        $this->client->index([
                            'index' => $this->indexName,
                            'body'  => $admissions[$j],
                            'id' => $admissions[$j]['id'],
                        ]);
                        echo 'Insert id: ' . $admissions[$j]['id'] . "\n";
                    }
                }
            }
        } catch (Throwable $exception) {
            $this->output->writeln(
                sprintf(
                    '<error>Error insert data into index %s, exception message: %s.</error>',
                    $this->indexName,
                    $exception->getMessage()
                )
            );

            exit();
        }

        $this->output->writeln(
            sprintf(
                '<info>Data of %s inserted.</info>',
                $this->indexName
            )
        );

        exit();
    }

    private function getElasticsearchData($from, $size)
    {
        $datas = $this->client->search([
            'index' => $this->indexName,
            'size' => $size,
            'from' => $from
        ]);

        return $datas['hits']['hits'];
    }

    private function transformDataAdmission($data)
    {
        return [
            'id' => Arr::get($data, 'id'),
            'slug' => Arr::get($data, 'slug'),
            'code' => Arr::get($data, 'code'),
            'name' => Arr::get($data, 'name'),
            'posts' => [],
            'created_at' => strtotime(Arr::get($data, 'created_at')),
            'updated_at' => strtotime(Arr::get($data, 'updated_at')),
        ];
    }

    private function transformDataAdmissionPost($code, $data)
    {
        return [
            'id' => Arr::get($data, 'id'),
            'slug' => Arr::get($data, 'slug'),
            'code' => $code,
            'name' => Arr::get($data, 'name'),
            'type' => Arr::get($data, 'type'),
            'admission_id' => Arr::get($data, 'admission_id'),
            'created_at' => strtotime(Arr::get($data, 'created_at')),
            'updated_at' => strtotime(Arr::get($data, 'updated_at')),
        ];
    }
}
