<?php

namespace Cart\Http\Controllers\Frontend;

use Barryvdh\Debugbar\Controllers\BaseController;
use Base\Mail\Checkout;
use Base\Mail\CreateCheckout;
use Base\Models\TrackingTryStudy;
use Base\Models\TrackingAddToCart;
use Base\Models\TrackingViewLanding;
use Base\Models\TrackingViewLandingData;
use Base\Models\TrackingViewLandingDataPhone;
use Cart\Models\Order;
use Cart\Models\OrderDetail;
use Cart\Models\OrderPackage;
use Coupon\Models\Coupon;
use Course\Models\Course;
use Cart\Services\CartServices;
use Gloudemans\Shoppingcart\Facades\Cart;
use Illuminate\Http\Request;
use Base\Supports\FlashMessage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Cookie;
use Carbon\Carbon;
use Omnipay\Omnipay;
use Auth;

class CartController extends BaseController
{
    public function postAddToCart(Request $request)
    {
        if ($request->course_id) {
            $result = $this->actionAddToCart($request->course_id, $request);

            if ($request->expectsJson()) {
                return response()->json($result, 200);
            } else {
                if (Auth::check()) {
                    if (!empty($request->couponCode)) {
                        return redirect(route('front.cart.checkout.get', ['couponCode' => $request->couponCode]));
                    }

                    return redirect(route('front.cart.checkout.get'))->with('success', $result['message']);
                } else {
                    return redirect()->back()->with('success', $result['message']);
                }
            }
        }

        return back()->with('error', 'Đã xảy ra lỗi! Vui lòng thử lại.');
    }

    private function actionAddToCart($course_id, Request $request)
    {
        $course = Course::find($course_id);
        $result = $course ? CartServices::checkCourse($course) : null;

        if (isset($result['code'])) {
            if (Auth::check()) {
                DB::transaction(function () use ($course_id, $request) {
                    $user = Auth::user();

                    $trackingViewLanding = [
                        'user_id' => $user->id,
                        'landing_id' => 'them-khoa-hoc-vao-gio-hang',
                        'ip' => $request->ip(),
                        'utm_source' => session('utm_source'),
                        'utm_medium' => session('utm_medium'),
                        'utm_campaign' => session('utm_campaign'),
                        'utm_term' => session('utm_term'),
                        'utm_content' => session('utm_content'),
                    ];

                    TrackingViewLanding::create($trackingViewLanding);

                    $trackingViewLandingData = [
                        'user_id' => $user->id,
                        'landing_id' => 'them-khoa-hoc-vao-gio-hang',
                        'name' => $user->first_name,
                        'phone' => $user->phone,
                        'email' => $user->email,
                        'position' => 'học sinh',
                        'utm_source' => session('utm_source'),
                        'utm_medium' => session('utm_medium'),
                        'utm_campaign' => session('utm_campaign'),
                        'utm_term' => session('utm_term'),
                        'utm_content' => session('utm_content'),
                    ];

                    $data = TrackingViewLandingData::firstOrCreate($trackingViewLandingData);
                    $data->ip = $request->ip();
                    $data->save();

                    $trackingViewLandingDataPhone = TrackingViewLandingDataPhone::firstOrCreate([
                        'phone' => $user->phone,
                    ]);

                    $trackingViewLandingDataPhone->update(['updated_at' => date('Y-m-d H:i:s')]);
                    TrackingAddToCart::firstOrCreate([
                        'user_id' => Auth::id(),
                        'course_id' => $course_id,
                        'utm_source' => session('utm_source'),
                        'utm_medium' => session('utm_medium'),
                        'utm_campaign' => session('utm_campaign'),
                        'utm_term' => session('utm_term'),
                        'utm_content' => session('utm_content'),
                    ]);
                });
            }

            CartServices::addToCart($course);
        }

        return $result;
    }

    public function postRemoveToCart(Request $request)
    {
        $result = CartServices::removeFromCart($request->cartId);

        return $result;
    }

    public function myCart(Request $request)
    {
        // call ajax at header area
        if ($request->expectsJson()) {
            [$comboInCart, $courseIdNotCombo] = CartServices::getComboOfCart();

            return response()->json([
                'html' => view('nqadmin-dashboard::frontend.components.header.dropdown.cart.list', [
                    'cart' => Cart::content(),
                    'comboInCart' => $comboInCart,
                    'courseIdNotCombo' => $courseIdNotCombo,
                ])->render(),
            ]);
        }
    }

    public function getCheckout(Request $request)
    {
        $user = Auth::user();
        $message = '';
        $coupon = null;
        $comboInCart = [];
        $courseIdNotCombo = [];
        $hasCoupon = false;
        $cart = Cart::content();

        if ($request->couponCode) {
            $coupon = Coupon::where('code', $request->couponCode)
                ->whereStatus('active')
                ->where('deadline', '>', date('Y-m-d H:i:s'))
                ->where('reamain', '>', 0)
                ->first();

            if ($coupon && $cart->where('id', $coupon->course)->count() > 0) {
                $message = 'Mã khuyến mại được áp dụng thành công! (Bạn chỉ có thể chọn một: Ưu đãi theo combo hoặc mã khuyến mại)!';
                $hasCoupon = true;
            } else {
                $message = 'Mã khuyến mại không đúng hoặc hết hạn!';
            }
        }

        if (!$hasCoupon) {
            [$comboInCart, $courseIdNotCombo] = CartServices::getComboOfCart();
        }

        return view('nqadmin-cart::frontend.checkout', [
            'user' => $user,
            'message' => $message,
            'coupon' => $coupon,
            'comboInCart' => $comboInCart,
            'courseIdNotCombo' => $courseIdNotCombo,
            'cart' => $cart,
            'hasCoupon' => $hasCoupon,
        ]);
    }

    public function postCheckoutFree($id, Request $request)
    {
        $users = Auth::user();
        $course = Course::find($id);

        if (!empty($course) && $course->price == 0) {
            $code = rand(1000000000, 9999999999);
            $check = Order::where('code', $code)->first();
            while (!empty($check)) {
                $code = rand(1000000000, 9999999999);
                $check = Order::where('code', $code)->first();
            }

            $order = Order::create([
                'code' => $code,
                'customer' => $users->id,
                'total_price' => 0,
                'payment_method' => 'direct',
                'status' => 'done',
                'type' => 'normal',
            ]);

            OrderDetail::create([
                'code' => $code,
                'order_id' => $order->id,
                'course_id' => $id,
                'author' => $course->owner->id ?? 1,
                'customer' => $users->id,
                'price' => 0,
                'base_price' => 0,
                'coupon_id' => null,
                'status' => 'done',
            ]);

            // if (isValidMail($users->email, $users->verify_email)) {
            //     Mail::to($users)->queue(new Checkout($users, $order, $order->status));
            // }

            return redirect(route('front.course.get', ['slug' => $course->slug]))
                ->with('success', 'bạn đã tham gia khóa học thành công!');
        }

        return redirect(route('front.home.index.get'))->with('error', 'Có lỗi xảy ra, xin thử lại!');
    }

    public function postCheckout(Request $request)
    {
        try {
            DB::beginTransaction();

            if (!$request->phone || !$request->first_name) {
                return back()->with('error', 'Vui lòng nhập đầy đủ thông tin người mua!');
            }

            if (!in_array($request->payment_method, Order::PAYMENT_METHOD)) {
                return back()->with('error', 'Vui lòng chọn phương thức thanh toán hợp lệ!');
            }

            $user = Auth::user();

            if ($request->first_name != $user->first_name || $request->phone != $user->phone) {
                $user->update([
                    'first_name' => $request->first_name,
                    'phone' => $request->phone,
                ]);
            }

            $payment_method = $request->payment_method;
            $cart = Cart::content();
            $hasCoupon = false;
            $coupon = null;
            $totalAmount = 0;

            if ($request->couponCode) {
                $coupon = Coupon::where('code', $request->couponCode)
                    ->whereStatus('active')
                    ->where('deadline', '>', date('Y-m-d H:i:s'))
                    ->where('reamain', '>', 0)
                    ->first();

                if ($coupon && $cart->where('id', $coupon->course)->count() > 0) {
                    $hasCoupon = true;
                }
            }

            if ($hasCoupon) {
                $totalAmount += $cart->sum('price') - $coupon->price;
            } else {
                [$comboInCart, $courseIdNotCombo] = CartServices::getComboOfCart();

                $totalAmount += $cart->whereIn('id', $courseIdNotCombo)->sum('price');

                if (!empty($comboInCart)) {
                    $totalAmount += collect($comboInCart)->sum('price');
                }
            }

            $code = rand(1000000000, 9999999999);
            $check = Order::where('code', $code)->first();

            while (!empty($check)) {
                $code = rand(1000000000, 9999999999);
                $check = Order::where('code', $code)->first();
            }

            $code_checkout = null;

            if ($payment_method == 'cod') {
                $code_checkout = rand(1000000000, 9999999999);
                $check = Order::where('code_checkout', $code_checkout)->first();

                while (!empty($check)) {
                    $code_checkout = rand(1000000000, 9999999999);
                    $check = Order::where('code_checkout', $code_checkout)->first();
                }
            }

            $order = Order::create([
                'code' => $code,
                'customer' => $user->id,
                'total_price' => $totalAmount,
                'payment_method' => $payment_method,
                'code_checkout' => $code_checkout,
                'address' => $request->address,
                'status' => 'create',
                'type' => 'normal',
            ]);

            // if (isValidMail($user->email, $user->verify_email)) {
            //     Mail::to($user)->queue(new Checkout($user, $order, $order->status));
            // }

            if ($hasCoupon) {
                foreach ($cart as $cartItem) {
                    $price = $cartItem->price;
                    $note = '';

                    if ($coupon && $cartItem->id == $coupon->course) {
                        $price = $price - $coupon->price;
                        $note = 'Sử dụng mã giảm giá: ' . $coupon->code . ' (Được giảm' . number_format($coupon->price) . ' đ)';
                    }

                    OrderDetail::create([
                        'code' => $code,
                        'order_id' => $order->id,
                        'course_id' => $cartItem->id,
                        'author' => $cartItem->options->owner_id ?? 1,
                        'customer' => $user->id,
                        'price' => $price,
                        'base_price' => $cartItem->price,
                        'note' => $note,
                        'status' => 'create',
                        'utm_source' => $this->getUtmSource($user->id, $cartItem->id),
                    ]);
                }
            } else {
                foreach($comboInCart as $combo) {
                    foreach($cart->whereIn('id', $combo->courseIds) as $cartItem) {
                        OrderDetail::create([
                            'code' => $code,
                            'order_id' => $order->id,
                            'course_id' => $cartItem->id,
                            'author' => $cartItem->options->owner_id ?? 1,
                            'customer' => $user->id,
                            'price' => $combo->price / count($combo->courseIds),
                            'base_price' => $cartItem->price,
                            'status' => 'create',
                            'note' => 'Combo: ' . $combo->name . ' (' . number_format($combo->price) . ' đ)',
                            'utm_source' => $this->getUtmSource($user->id, $cartItem->id),
                        ]);
                    }
                }

                foreach($cart->whereIn('id', $courseIdNotCombo) as $cartItem) {
                    OrderDetail::create([
                        'code' => $code,
                        'order_id' => $order->id,
                        'course_id' => $cartItem->id,
                        'author' => $cartItem->options->owner_id ?? 1,
                        'customer' => $user->id,
                        'price' => $cartItem->price,
                        'base_price' => $cartItem->price,
                        'status' => 'create',
                        'utm_source' => $this->getUtmSource($user->id, $cartItem->id),
                    ]);
                }
            }

            DB::commit();

            if ($totalAmount == 0) {
                $order->done();
                CartServices::removeAllFromCart();

                return redirect(route('front.users.my_course.get'))->with('success', 'Bạn đã mua khóa học thành công!');
            }

            return $this->handleCheckout($payment_method, $order->code, $totalAmount);
        } catch (\Exception $e) {
            DB::rollBack();
            report($e);

            return redirect(route('front.home.index.get'))->with('error', 'Có lỗi xảy ra, xin thử lại!');
        }
    }

    public function getPayment(Request $request)
    {
        $package = null;

        if (request('package')) {
            $package = config('web.packages.' . request('package')) ?? null;
        }

        if (!$package) {
            return back()->with('error', 'Dịch vụ không tồn tại.');
        }

        $user = Auth::user();

        if (!$package['active'] && !$user->isEditor()) {
            return redirect()->route('dang-ky-vip')->with('error', 'Gói VIP này chỉ dành cho các bạn đã mua sách của chúng tôi tại: https://shopee.vn/vietjack_official_store');
        }

        return view('nqadmin-cart::frontend.payment', [
            'user' => $user,
            'package' => $package,
        ]);
    }

    public function submitPayment(Request $request)
    {
        try {
            $package = null;

            if ($request->package) {
                $package = config('web.packages.' . $request->package) ?? null;
            }

            if (!$package) {
                return back()->with('error', 'Dịch vụ không tồn tại.');
            }

            if (!in_array($request->payment_method, Order::PAYMENT_METHOD)) {
                return back()->with('error', 'Vui lòng chọn phương thức thanh toán hợp lệ!');
            }

            $user = Auth::user();

            if (!$package['active'] && !$user->isEditor()) {
                return redirect()->route('dang-ky-vip')->with('error', 'Gói VIP này chỉ dành cho các bạn đã mua sách của chúng tôi tại: https://shopee.vn/vietjack_official_store');
            }

            DB::beginTransaction();

            if ($request->phone || $request->first_name) {
                $user->update([
                    'first_name' => $request->first_name,
                    'phone' => $request->phone,
                ]);
            }

            $payment_method = $request->payment_method;
            $totalAmount = $package['price'];

            $code = rand(1000000000, 9999999999);
            $check = Order::where('code', $code)->first();

            while (!empty($check)) {
                $code = rand(1000000000, 9999999999);
                $check = Order::where('code', $code)->first();
            }

            $vipUtm = request()->cookie('_vj_vip_');
            $utmArray = $vipUtm ? json_decode($vipUtm, true) : null;
            $expired_at = Carbon::now()->addMonths($package['duration']);
            $order = Order::create([
                'code' => $code,
                'customer' => $user->id,
                'total_price' => $totalAmount,
                'payment_method' => $payment_method,
                'status' => 'create',
                'type' => $request->package,
                'utm' => $utmArray,
                'utm_source' => $utmArray['utm_source'] ?? null,
            ]);
            $orderPackage = OrderPackage::updateOrCreate([
                'customer' => $user->id,
                'package' => $request->package,
                'status' => OrderPackage::DISABLED,
            ], [
                'order_id' => $order->id,
                'date_duration' => $package['duration'],
                'expired_at' => $expired_at,
            ]);

            if ($request->course) {
                $course = Course::find($request->course);

                if ($course && $course->with_vip == $request->package) {
                    OrderDetail::create([
                        'code' => $code,
                        'order_id' => $order->id,
                        'course_id' => $course->id,
                        'author' => $course->author,
                        'customer' => $user->id,
                        'price' => $totalAmount,
                        'base_price' => $course->price,
                        'note' => 'Khóa học đính kèm gói VIP (giá bán sẽ lấy giá của gói VIP)',
                        'status' => 'create',
                        'expired_date' => $expired_at,
                    ]);
                }
            }

            DB::commit();

            return $this->handlePayment($payment_method, $order->code, $totalAmount, $orderPackage->id);
        } catch (\Exception $e) {
            DB::rollBack();
            report($e);

            return redirect(route('front.home.index.get'))->with('error', 'Có lỗi xảy ra, xin thử lại!');
        }
    }

    private function onePayDomesticGateway()
    {
        $gateway = Omnipay::create(config('payment.gateways.OnePayDomestic.driver'));

        $gateway->initialize([
            'vpc_Merchant' => config('payment.gateways.OnePayDomestic.options.vpcMerchant'),
            'vpc_AccessCode' => config('payment.gateways.OnePayDomestic.options.vpcAccessCode'),
            'vpc_User' => config('payment.gateways.OnePayDomestic.options.vpcUser'),
            'vpc_Password' => config('payment.gateways.OnePayDomestic.options.vpcPassword'),
            'vpc_HashKey' => config('payment.gateways.OnePayDomestic.options.vpcSecureHash'),
            'testMode' => config('payment.testMode'),
        ]);

        return $gateway;
    }

    private function onePayInternationalGateway()
    {
        $gateway = Omnipay::create(config('payment.gateways.OnePayInternational.driver'));

        $gateway->initialize([
            'vpc_Merchant' => config('payment.gateways.OnePayInternational.options.vpcMerchant'),
            'vpc_AccessCode' => config('payment.gateways.OnePayInternational.options.vpcAccessCode'),
            'vpc_User' => config('payment.gateways.OnePayInternational.options.vpcUser'),
            'vpc_Password' => config('payment.gateways.OnePayInternational.options.vpcPassword'),
            'vpc_HashKey' => config('payment.gateways.OnePayInternational.options.vpcSecureHash'),
            'testMode' => config('payment.testMode'),
        ]);

        return $gateway;
    }

    private function vnPayGateway()
    {
        $gateway = Omnipay::create(config('payment.gateways.VNPay.driver'));

        $gateway->initialize([
            'vnp_TmnCode' => config('payment.gateways.VNPay.options.vnpTmnCode'),
            'vnp_HashSecret' => config('payment.gateways.VNPay.options.vnpHashSecret'),
            'testMode' => config('payment.testMode'),
        ]);

        return $gateway;
    }

    private function handleCheckout($payment_method, $orderCode, $totalPrice)
    {
        switch ($payment_method) {
            case 'transfer':
            case 'direct':
                CartServices::removeAllFromCart();

                return redirect()->route('front.users.history.get')
                    ->with('success', 'Bạn đã thanh toán thành công, Chờ xác nhận từ quản trị viên!');
                break;

            case 'cod':
                CartServices::removeAllFromCart();

                return redirect()->route('front.users.history.get')
                    ->with('success', 'Thông tin của bạn đã được gửi tới tư vấn viên của VietJack, chúng tôi sẽ liên hệ với bạn trong 24h!');
                break;

            // case 'atm_onepay':
            //     $gateway = $this->onePayDomesticGateway();
            //     $response = $gateway->purchase([
            //         'Title' => 'Domestic',
            //         'AgainLink' => route('front.cart.checkout.get'),
            //         'vpc_MerchTxnRef' => $orderCode,
            //         'vpc_ReturnURL' => route('front.cart.payment.return', ['type' => 'atm']),
            //         'vpc_TicketNo' => request()->ip(),
            //         'vpc_Amount' => $totalPrice * 100, // Document of onepay: do not use decimal for amount for testing (100=1VND -> right; 120=1.2VND -> wrong)
            //         'vpc_OrderInfo' => 'VietJack #' . $orderCode,
            //     ])->send();

            //     if ($response->isRedirect()) {
            //         header("Location: " . $response->getRedirectUrl());
            //         exit(0);
            //     }

            //     break;

            // case 'visa_onepay':
            //     $gateway = $this->onePayInternationalGateway();
            //     $response = $gateway->purchase([
            //         'Title' => 'International',
            //         'AgainLink' => route('front.cart.checkout.get'),
            //         'vpc_MerchTxnRef' => $orderCode,
            //         'vpc_ReturnURL' => route('front.cart.payment.return', ['type' => 'visa']),
            //         'vpc_TicketNo' => request()->ip(),
            //         'vpc_Amount' => $totalPrice * 100, // Document of onepay: do not use decimal for amount for testing (100=1VND -> right; 120=1.2VND -> wrong)
            //         'vpc_OrderInfo' => 'VietJack #' . $orderCode,
            //     ])->send();

            //     if ($response->isRedirect()) {
            //         header("Location: " . $response->getRedirectUrl());
            //         exit(0);
            //     }

            //     break;

            case 'vnpay':
                $gateway = $this->vnPayGateway();
                $response = $gateway->purchase([
                    'vnp_TxnRef' => $orderCode,
                    'vnp_OrderType' => 250000, // Mã danh mục hàng hóa. Mỗi hàng hóa sẽ thuộc một nhóm danh mục do VNPAY quy định: https://sandbox.vnpayment.vn/apis/docs/loai-hang-hoa/
                    'vnp_OrderInfo' => 'Thanh toan dich vu tren vietjack',
                    'vnp_IpAddr' => request()->ip(),
                    'vnp_CreateDate' => date('YmdHis'),
                    'vnp_Amount' => $totalPrice * 100,
                    'vnp_ReturnUrl' => route('front.cart.payment.return', ['type' => 'vnpay']),
                ])->send();

                if ($response->isRedirect()) {
                    header("Location: " . $response->getRedirectUrl());
                    exit(0);
                }

                break;
        }
    }

    private function handlePayment($payment_method, $orderCode, $totalPrice, $orderPackageId)
    {
        switch ($payment_method) {
            case 'transfer':
            case 'direct':
                return redirect()->route('front.users.history.get')
                    ->with('success', 'Bạn đã thanh toán thành công! Vui lòng gọi điện đến số hotline: ' . config('app.hotline') . ' để xác nhận với quản trị viên.');
                break;

            case 'vnpay':
                $gateway = $this->vnPayGateway();
                $response = $gateway->purchase([
                    'vnp_TxnRef' => $orderCode,
                    'vnp_OrderType' => 250000, // Mã danh mục hàng hóa. Mỗi hàng hóa sẽ thuộc một nhóm danh mục do VNPAY quy định: https://sandbox.vnpayment.vn/apis/docs/loai-hang-hoa/
                    'vnp_OrderInfo' => 'Thanh toan dich vu tren vietjack',
                    'vnp_IpAddr' => request()->ip(),
                    'vnp_CreateDate' => date('YmdHis'),
                    'vnp_Amount' => $totalPrice * 100,
                    // 'vnp_ExpireDate' => date('Ymdhis'),
                    'vnp_ReturnUrl' => route('front.cart.payment.return', [
                        'type' => 'vnpay',
                        'package' => $orderPackageId,
                    ]),
                    'vnp_BankCode' => 'VNPAYQR',
                ])->send();

                if ($response->isRedirect()) {
                    $redirectUrl = $response->getRedirectUrl();

                    header("Location: " . $response->getRedirectUrl());
                    exit(0);
                }

                break;
        }
    }

    public function paymentReturn(Request $request)
    {
        $message = '';

        try {
            $gateway = null;

            // if ($request->type == 'visa') {
            //     $gateway = $this->onePayInternationalGateway();
            //     $key_code = 'vpc_MerchTxnRef';
            //     $key_status = 'vpc_TxnResponseCode';
            // }

            // if ($request->type == 'atm') {
            //     $gateway = $this->onePayDomesticGateway();
            //     $key_code = 'vpc_MerchTxnRef';
            //     $key_status = 'vpc_TxnResponseCode';
            // }

            if ($request->type == 'vnpay') {
                $gateway = $this->vnPayGateway();
                $key_code = 'vnp_TxnRef';
                $key_status = 'vnp_ResponseCode';
                $key_transaction_code = 'vnp_TransactionNo';
            }

            if ($gateway) {
                $response = $gateway->completePurchase()->send();
                $code = $response->{$key_code};
                $order = Order::where('code', $code)
                    ->where('customer', auth()->id())
                    ->first();

                if ($order) {
                    if ($response->isSuccessful()) {
                        $status = 'done';
                        $message = 'Bạn đã thanh toán! Xin vui lòng chờ trong giây lát để dịch vụ được kích hoạt và bắt đầu trải nghiệm với đầy đủ tính năng dành cho bạn.';

                        if (!$request->package) {
                            CartServices::removeAllFromCart();
                        }
                    } else {
                        $status = 'cancel';
                        $message = 'Thanh toán thất bại! Vui lòng thử lại hoặc liên hệ với chúng tôi nếu không thực hiện được.';
                    }

                    $order->update([
                        'status' => $status,
                        'token' => $response->{$key_status} . '-' . $response->{$key_transaction_code}, // Dạng: trạng thái - mã giao dịch
                    ]);
                } else {
                   $message = 'Đơn hàng không tồn tại! Vui lòng thử lại.';
                }
            } else {
                $message = 'Thanh toán không được hỗ trợ! Vui lòng thử lại.';
            }
        } catch (\Exception $e) {
            report($e);

            $message = $e->getMessage();
        } finally {
            Cookie::queue(Cookie::forget('_vj_vip_'));

            return redirect(route('front.users.history.get'))
                ->with('success', $message);
        }
    }

    public function vnpayIpn(Request $request)
    {
        $returnData = [];

        try {
            DB::beginTransaction();

            $gateway = $this->vnPayGateway();

            if ($gateway) {
                $response = $gateway->completePurchase()->send();
                $code = $response->vnp_TxnRef;
                $order = Order::where('code', $code)
                    ->first();

                if ($order) {
                    if ($order->total_price * 100 == $response->vnp_Amount) {
                        if ($response->isSuccessful()) {
                            $order->done();

                            // $users = $order->getCustomer;
                            // if (isValidMail($users->email, $users->verify_email)) {
                            //     Mail::to($users)->queue(new Checkout($users, $order, $order->status));
                            // }
                        } else {
                            $order->cancel();
                        }

                        DB::commit();

                        $returnData['RspCode'] = '00';
                        $returnData['Message'] = 'Confirm Success';
                    } else {
                        $returnData['RspCode'] = '04';
                        $returnData['Message'] = 'invalid amount';
                    }
                } else {
                    $returnData['RspCode'] = '01';
                    $returnData['Message'] = 'Order not found';
                }
            } else {
                $returnData['RspCode'] = '99';
                $returnData['Message'] = 'Unknow error';
            }
        } catch (\Exception $e) {
            report($e);
            DB::rollBack();

            $returnData['RspCode'] = '97';
            $returnData['Message'] = 'Invalid signature';
        } finally {
            return response()->json($returnData, 200);
        }
    }

    public function paymentQueryTransaction(Request $request)
    {
        try {
            $response = null;

            if ($request->payment_method == 'visa') {
                $gateway = $this->onePayInternationalGateway();
                $response = $gateway->queryTransaction([
                    'vpc_MerchTxnRef' => $request->code,
                ])->send();
            }

            if ($request->payment_method == 'atm') {
                $gateway = $this->onePayDomesticGateway();
                $response = $gateway->queryTransaction([
                    'vpc_MerchTxnRef' => $request->code,
                ])->send();
            }

            if ($request->payment_method == 'vnpay') {
                $gateway = $this->vnPayGateway();
                $response = $gateway->queryTransaction([
                    'vnp_TransDate' => date('YmdHis'),
                    'vnp_TxnRef' => $request->code,
                    'vnp_OrderInfo' => 'Thanh toan dich vu tren vietjack',
                    'vnp_IpAddr' => request()->ip(),
                    'vnp_TransactionNo' => 1,
                ])->send();
            }

            if ($response) {
                return response()->json([
                    'message' => $response->getMessage(),
                    'data' => $response->getData(),
                ], 200);
            }

            return response()->json([
                'message' => 'Gateway not found!',
            ], 200);
        } catch (\Exception $e) {
            report($e);

            return response()->json([
                'message' => $e->getMessage(),
            ], 200);
        }
    }

    public function detectAction(Request $request)
    {
        session(['action' => $request->action]);
    }

    public function getUtmSource($userId, $courseId)
    {
        $utmSource = 'web';

        if ($userId) {
            $trackingStudy = TrackingTryStudy::where('user_id', $userId)
                ->where('course_id', $courseId)
                ->where(DB::raw('date(created_at)'), '>=', DB::raw('date(now() - interval 90 day)'))
                ->orderBy('id', 'desc')
                ->first(); // utm_source chỉ có hiệu lực trong vòng 90 ngày

            $utmSource = $trackingStudy->utm_source ?? $utmSource;
        }

        return $utmSource;
    }
}
