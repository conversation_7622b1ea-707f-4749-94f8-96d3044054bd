<?php

namespace Cart\Models;

use Course\Models\Course;
use Illuminate\Database\Eloquent\Model;
use Users\Models\OrderInvoice;
use Users\Models\Users;

class Order extends Model
{
    protected $table = 'orders';
    protected $fillable = [
        'code',
        'customer',
        'total_price',
        'payment_method',
        'status',
        'token',
        'admin_create',
        'admin_update',
        'type',
        'code_checkout',
        'address',
        'utm',
        'utm_source',
    ];

    protected $casts = [
        'utm' => 'array',
    ];

    // muốn thêm 1 method cần viết migration để update enum type của cột payment_method
    const PAYMENT_METHOD = ['transfer', 'atm', 'visa', 'vnpay', 'direct', 'phone', 'cod'];

    /**
     * Relation 1 - n with Detail
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function detail()
    {
        return $this->hasMany(OrderDetail::class, 'order_id');
    }

    public function orderPackages()
    {
        return $this->hasMany(OrderPackage::class, 'order_id');
    }

    public function getInvoice()
    {
        return $this->hasOne(OrderInvoice::class, 'order_id');
    }

    public function getCustomer()
    {
        return $this->belongsTo(Users::class, 'customer');
    }

    public function getCourse()
    {
        return $this->belongsToMany(Course::class, 'order_details', 'order_id', 'course_id');
    }

    public function getStatusTextAttribute()
    {
        switch ($this->status) {
            case 'create':
                $text = 'Đang chờ thanh toán';
                break;
            case 'done':
                $text = 'Đã thanh toán';
                break;
            case 'cancel':
                $text = 'Đã hủy đơn hàng';
                break;
            default:
                $text = 'Đang chờ thanh toán';
                break;
        }

        return $text;
    }

    public function getTextStatusAttribute()
    {
        switch ($this->status) {
            case 'create':
                $class = 'warning';
                $text = 'Chưa thanh toán';
                break;
            case 'done':
                $class = 'success';
                $text = 'Đã thanh toán';
                break;
            case 'cancel':
                $class = 'warning';
                $text = 'Đã hủy đơn hàng';
                break;
            case 'reject':
                $class = 'warning';
                $text = 'Đã hủy đơn hàng lỗi';
                break;
            default:
                $class = 'warning';
                $text = 'Chưa thanh toán';
                break;
        }

        return ['class' => $class, 'text' => $text];
    }

    public function getTextPaymentMethodAttribute()
    {
        switch ($this->payment_method) {
            case 'transfer':
                $text = 'Chuyển khoản ngân hàng';
                break;
            case 'atm':
                $text = 'Thẻ ATM có Internet Baking';
                break;
            case 'visa':
                $text = 'VISA/Master Card';
                break;
            case 'direct':
                $text = 'Thanh toán trực tiếp';
                break;
            case 'phone':
                $text = 'Thanh toán qua điện thoại';
                break;
            case 'cod':
                $text = 'Ship Code';
                break;
            case 'vnpay':
                $text = 'Thanh toán online qua vnpay';
                break;
            default:
                $text = 'Không xác định';
        }
        return $text;
    }

    public function done()
    {
        foreach ($this->detail as $item) {
            $item->status = 'done';
            $item->save();
        }

        foreach ($this->orderPackages as $item) {
            $item->status = OrderPackage::ACTIVE;
            $item->save();
        }

        $this->update(['status' => 'done']);
    }

    public function cancel()
    {
        foreach ($this->detail as $item) {
            $item->status = 'cancel';
            $item->save();
        }

        foreach ($this->orderPackages as $item) {
            $item->status = OrderPackage::DISABLED;
            $item->save();
        }

        $this->update(['status' => 'cancel']);
    }

    public function reject()
    {
        foreach ($this->detail as $item) {
            $item->status = 'reject';
            $item->save();
        }
        $this->update(['status' => 'reject']);
    }

    public function userCreated()
    {
        return $this->belongsTo(Users::class, 'admin_create');
    }

    public function userUpdated()
    {
        return $this->belongsTo(Users::class, 'admin_update');
    }
}
