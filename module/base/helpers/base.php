<?php

use Base\Supports\FlashMessage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Request;
use \Firebase\JWT\JWT;

if (!function_exists('is_in_dashboard')) {
    /**
     * @return bool
     */
    function is_in_dashboard()
    {
        $segment = request()->segment(1);
        if ($segment === config('SOURCE_ADMIN_ROUTE', 'admincp')) {
            return true;
        }

        return false;
    }
}

if (!function_exists('convert_status')) {
    function conver_status($status)
    {
        switch ($status) {
            case 'active' :
                return '<span class="status success">K<PERSON>ch hoạt</span>';
                break;
            case 'disable' :
                return '<span class="status danger">Tạm khóa</span>';
                break;
            default:
                return '<span class="status danger">Tạm khóa</span>';
                break;
        }
    }
}

if (!function_exists('convert_status')) {
    function convert_active($status)
    {
        switch ($status) {
            case '1' :
                return '<span class="status success"><PERSON><PERSON><PERSON> ho<PERSON></span>';
                break;
            case '0' :
                return '<span class="status danger"><PERSON>ư<PERSON> kích hoạt</span>';
                break;
            default:
                return '<span class="status danger">Chưa thanh toán</span>';
                break;
        }
    }
}

if (!function_exists('convert_flash_message')) {
    function convert_flash_message($mess = 'create')
    {
        switch ($mess) {
            case 'create':
                $m = config('messages.success_create');
                break;
            case 'edit':
                $m = config('messages.success_edit');
                break;
            case 'delete':
                $m = config('messages.success_delete');
                break;
            default:
                $m = config('messages.success_create');
        }

        return $m;
    }
}

if (!function_exists('changeStatus')) {
    function changeStatus($id, $repository)
    {
        try {
            $record = $repository->find($id);
            $status = ($record->status == 'active') ? 'disable' : 'active';
            $repository->update([
                'status' => $status
            ], $id);

            return redirect()->back()->with(FlashMessage::returnMessage('edit'));
        } catch (\Exception $e) {
            report($e);
            return redirect()->back()->withErrors(config('messages.error'));
        }
    }
}

if (!function_exists('setFeatured')) {
    function setFeatured($id, $repository)
    {
        try {
            $record = $repository->find($id);
            $status = ($record->featured == 'active') ? 'disable' : 'active';
            $repository->update([
                'featured' => $status
            ], $id);

            return redirect()->back()->with(FlashMessage::returnMessage('edit'));
        } catch (\Exception $e) {
            report($e);
            return redirect()->back()->withErrors(config('messages.error'));
        }
    }
}

if (!function_exists('getDelete')) {
    function getDelete($id, $repository)
    {
        try {
            $repository->delete($id);
            return redirect()->back()->with(FlashMessage::returnMessage('delete'));
        } catch (\Exception $e) {
            report($e);
            return redirect()->back()->withErrors(config('messages.error'));
        }
    }
}

if (!function_exists('time_elapsed_string')) {
    function time_elapsed_string($time)
    {
        $time = time() - strtotime($time); // to get the time since that moment
        $time = ($time < 1) ? 1 : $time;
        $tokens = array(
            31536000 => 'năm',
            2592000 => 'tháng',
            604800 => 'tuần',
            86400 => 'ngày',
            3600 => 'giờ',
            60 => 'phút',
            1 => 'giây'
        );

        foreach ($tokens as $unit => $text) {
            if ($time < $unit) continue;
            $numberOfUnits = floor($time / $unit);
            return $numberOfUnits . ' ' . $text . ' trước';
        }
    }
}

if (!function_exists('secToHR')) {
    function secToHR($seconds, $short = false)
    {
        if (!$seconds) return '';
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds / 60) % 60);
        $seconds = $seconds % 60;
        if (!$short)
            return "$hours giờ, $minutes phút, $seconds giây";
        else {
            $hours = $hours < 10 ? '0' . $hours : $hours;
            $minutes = $minutes < 10 ? '0' . $minutes : $minutes;
            $seconds = $seconds < 10 ? '0' . $seconds : $seconds;
            return "$hours:$minutes:$seconds";
        }
    }
}

if (!function_exists('humanTiming')) {
    function humanTiming($time)
    {

        $time = time() > $time ? time() - $time : $time - time(); // to get the time since that moment

        $tokens = array(
            31536000 => 'năm',
            2592000 => 'tháng',
            604800 => 'tuần',
            86400 => 'ngày',
            3600 => 'giờ',
            60 => 'phút',
        );

        $result = '';
        $counter = 1;
        foreach ($tokens as $unit => $text) {
            if ($time < $unit) continue;
            if ($counter > 2) break;

            $numberOfUnits = floor($time / $unit);
            $result .= "$numberOfUnits $text ";
            $time -= $numberOfUnits * $unit;
            ++$counter;
        }

        return "{$result}";
    }
}

if (!function_exists('stripUnicode')) {
    function stripUnicode($str)
    {
        if (!$str || !is_string($str)) return false;

        $unicode = array(
            'a' => 'á|à|ả|ã|ạ|ă|ắ|ằ|ẳ|ẵ|ặ|â|ấ|ầ|ẩ|ẫ|ậ|&#227;|&#224;|&#225;|&#226;|&#226;́|&#226;̀|&#226;̣|&#226;̉|&#226;̃|á|à|ả|ạ|ắ|ằ|ẳ|ặ|ấ|ầ|ẩ|ẫ|ậ',
            'A' => 'Á|À|Ả|Ã|Ạ|Ă|Ắ|Ằ|Ẳ|Ẵ|Ặ|Â|Ấ|Ầ|Ẩ|Ẫ|Ậ|&#193;|&#192;|&#195;|&#194;|&#194;́|&#194;̀|&#194;̣|&#194;̉|&#194;̃|Á|À|Ả|Ạ|Ắ|Ằ|Ẳ|Ặ|Ấ|Ầ|Ẩ|Ẫ|Ậ',
            'd' => 'đ',
            'D' => 'Đ',
            'e' => 'é|è|ẻ|ẽ|ẹ|ê|ế|ề|ể|ễ|ệ|&#233;|&#232;|&#234;|&#234;́|&#234;̀|&#234;̣|&#234;̉|&#234;̃|é|è|ẻ|ẽ|ẹ|ế|ề|ể|ễ|ệ',
            'E' => 'É|È|Ẻ|Ẽ|Ẹ|Ê|Ế|Ề|Ể|Ễ|Ệ|&#201;|&#200;|&#202;|&#202;́|&#202;̀|&#202;̣|&#202;̉|&#202;̃|É|È|Ẻ|Ẽ|Ẹ|Ế|Ề|Ể|Ễ|Ệ',
            'i' => 'í|ì|ỉ|ĩ|ị|&#237;|&#236;|í|ì|ỉ|ị|ĩ',
            'I' => 'Í|Ì|Ỉ|Ĩ|Ị|&#205;|&#204;|Í|Ì|Ỉ|Ĩ|Ị',
            'o' => 'ó|ò|ỏ|õ|ọ|ô|ố|ồ|ổ|ỗ|ộ|ơ|ớ|ờ|ở|ỡ|ợ|&#243;|&#242;|&#245;|&#244;|&#244;̀|&#244;́|&#244;̣|&#244;̉|&#244;̃|ó|ò|ỏ|õ|ọ|ố|ồ|ổ|ỗ|ộ|ớ|ờ|ở|ợ|ỡ',
            'O' => 'Ó|Ò|Ỏ|Õ|Ọ|Ô|Ố|Ồ|Ổ|Ỗ|Ộ|Ơ|Ớ|Ờ|Ở|Ỡ|Ợ|&#211;|&#210;|&#213;|&#212;|&#212;̀|&#212;́|&#212;̣|&#212;̉|&#212;̃|Ó|Ò|Ỏ|Õ|Ọ|Ố|Ồ|Ổ|Ỗ|Ộ|Ớ|Ờ|Ở|Ỡ|Ợ',
            'u' => 'ú|ù|ủ|ũ|ụ|ư|ứ|ừ|ử|ữ|ự|&#250;|&#249;|ú|ù|ủ|ũ|ụ|ứ|ừ|ử|ữ|ự',
            'U' => 'Ú|Ù|Ủ|Ũ|Ụ|Ư|Ứ|Ừ|Ử|Ữ|Ự|&#218;|&#217;|Ú|Ù|Ử|Ũ|Ụ|Ứ|Ừ|Ử|Ữ|Ự',
            'y' => 'ý|ỳ|ỷ|ỹ|ỵ|&#253;|ý|ỳ|ỷ|ỹ|ỵ',
            'Y' => 'Ý|Ỳ|Ỷ|Ỹ|Ỵ|&#221;|Ý|Ỳ|Ỷ|Ỹ|Ỵ',
        );

        foreach ($unicode as $khongdau => $codau) {
            $arr = explode("|", $codau);
            $str = str_replace($arr, $khongdau, $str);
        }

        $str = str_replace("–", "-", $str);
        $str = str_replace(",", "-", $str);
        $str = str_replace("?", "-", $str);
        $str = str_replace('"', '-', $str);
        $str = str_replace("“", "-", $str);
        $str = str_replace("”", "-", $str);
        $str = str_replace(" ", "-", $str);
        $str = str_replace(":", "-", $str);
        $str = str_replace("’", "-", $str);
        $str = str_replace("'", "-", $str);
        $str = str_replace(".", "-", $str);
        $str = str_replace("!", "-", $str);
        $str = str_replace("(", "-", $str);
        $str = str_replace(")", "-", $str);
        $str = str_replace("[", "-", $str);
        $str = str_replace("]", "-", $str);
        $str = str_replace("%", "-", $str);
        $str = str_replace("/", "-", $str);
        $str = str_replace("*", "-", $str);
        $str = str_replace("#", "-", $str);
        $str = str_replace("~", "-", $str);
        $str = preg_replace("/-{2,5}/", "-", $str);
        $str = strtolower($str);

        return htmlspecialchars($str);
    }
}

if (!function_exists('checkTypeVideo')) {
    /**
     * Generate an asset path for the application.
     *
     * @param string $path
     * @param bool $secure
     * @return string
     */
    function checkTypeVideo($path)
    {
        if (!empty($path)) {
            $check = explode('.', $path);

            return $check[count($check) - 1];
        } else {
            return $path;
        }
    }
}

if (!function_exists('sourceTypeVideo')) {

    function sourceTypeVideo($path)
    {
        $sourceTypeVideo = 'video/mp4';
        $pathArr = explode('.', $path);

        if (end($pathArr) == 'm3u8') {
            $sourceTypeVideo = 'application/x-mpegURL';
        }

        return $sourceTypeVideo;
    }
}

if (!function_exists('assetMedia')) {
    /**
     * Generate an asset path for the application.
     *
     * @param string $path
     * @param bool $secure
     * @return string
     */
    function assetMedia($path)
    {
        $path = ltrim($path, '/');

        if (!empty($path)) {
            $baseUrl = config('app.env') == 'local' ? (config('app.video_url') . $path) : asset($path);
            $url = checkTypeVideo($path) == 'mp4' ? $baseUrl : config('app.video_url') . $path;

            return $url;
        } else {
            return $path;
        }
    }
}

if (!function_exists('reFormatDate')) {
    function reFormatDate($date)
    {
        $temp = explode('/', $date);

        return $temp[2] . '-' . $temp[1] . '-' . $temp[0];
    }
}

if (!function_exists('number_format_short')) {

    /**
     * @param $n
     * @return string
     * Converts a number into a short version, eg: 1000 -> 1k
     */
    function number_format_short( $n, $precision = 1 )
    {
        if ($n < 900) {
            // 0 - 900
            $n_format = number_format($n, $precision);
            $suffix = '';
        } else if ($n < 900000) {
            // 0.9k-850k
            $n_format = number_format($n / 1000, $precision);
            $suffix = ' K';
        } else if ($n < 900000000) {
            // 0.9m-850m
            $n_format = number_format($n / 1000000, $precision);
            $suffix = ' M';
        } else if ($n < 900000000000) {
            // 0.9b-850b
            $n_format = number_format($n / 1000000000, $precision);
            $suffix = ' B';
        } else {
            // 0.9t+
            $n_format = number_format($n / 1000000000000, $precision);
            $suffix = ' T';
        }
        // Remove unecessary zeroes after decimal. "1.0" -> "1"; "1.00" -> "1"
        // Intentionally does not affect partials, eg "1.50" -> "1.50"
        if ( $precision > 0 ) {
            $dotzero = '.' . str_repeat( '0', $precision );
            $n_format = str_replace( $dotzero, '', $n_format );
        }

        return $n_format . $suffix;
    }
}

if (!function_exists('number_format_short_2')) {

    /**
     * @param $n
     * @return string
     * Converts a number into a short version, eg: 1000 -> 1k
     */
    function number_format_short_2( $n, $precision = 1 )
    {
        if ($n < 900) {
            // 0 - 900
            $n_format = number_format($n, $precision);
            $suffix = '';
        } else if ($n < 900000) {
            // 0.9k-850k
            $n_format = number_format($n / 1000, $precision);
            $suffix = ' N';
        } else if ($n < 900000000) {
            // 0.9m-850m
            $n_format = number_format($n / 1000000, $precision);
            $suffix = ' Tr';
        } else if ($n < 900000000000) {
            // 0.9b-850b
            $n_format = number_format($n / 1000000000, $precision);
            $suffix = ' T';
        } else {
            // 0.9t+
            $n_format = number_format($n / 1000000000000, $precision);
            $suffix = ' NT'; // nghìn tỷ
        }
        // Remove unecessary zeroes after decimal. "1.0" -> "1"; "1.00" -> "1"
        // Intentionally does not affect partials, eg "1.50" -> "1.50"
        if ( $precision > 0 ) {
            $dotzero = '.' . str_repeat( '0', $precision );
            $n_format = str_replace( $dotzero, '', $n_format );
        }

        return $n_format . $suffix;
    }
}

if (!function_exists('timUCLN')) {

    /**
     * Tim UCLN
     *
     * @param array $array
     * @return int
     */
    function timUCLN($array)
    {
        $temp = 0;

        foreach ($array as $key => $next) {
            if ($key == 0) {
                $temp = $next;

                continue;
            }

            while ($temp * $next != 0) {
                if ($temp > $next) {
                    $temp %= $next;
                } else {
                    $next %= $temp;
                }
            }
            $temp = $temp + $next;
        }

        return $temp;
    }
}

if (!function_exists('getClipTo')) {

    function getClipTo($isPreview, $type = 0)
    {
        $clipTo = null;

        // if ($isPreview == 'disable' && $type != 2) {
        if ($type != 2) {
            if ($isPreview == 'disable') {
                $clipTo = $type == 1 ? 120000 : 60000;
            } else {
                $clipTo = $type == 1 ? null : 120000;
            }
        }

        return $clipTo;
    }
}

if (!function_exists('replaceBaseURL')) {

    function replaceBaseURL($id, $checkAuth = false, $checkBought = false, $quality = null)
    {
        // type = 0 : not login
        // type = 1 : is login and not bought
        // type = 2 : is login and bought
        $type = 0;

        if ($checkAuth) {
            $type = $checkBought ? 2 : 1;
        }

        try {
            $client = new \GuzzleHttp\Client();
            $endPoint = rtrim(config('app.video_url'), '/') . '/media/video-url/' . $id . '/' . $type . ($quality ? ('/' . $quality) : '');
            $res = $client->request('GET', $endPoint, [
                'http_errors' => false
            ]);
            $result = json_decode($res->getBody()->getContents(), true);

            return $result['url'];
        } catch (\Exception $e) {
            Log::error($e);

            return '';
        }
    }
}

/**
 * Check if an input has error, e.g. validation errors from Laravel
 *
 * @param array|string $fields Input fields name
 * @param \Illuminate\Support\MessageBag $errors
 * @param string $errorCssClass   Css class when field has error
 *
 * @return string
 */
function has_error($fields, $errors, $errorCssClass = 'has-error')
{
    return $errors->hasAny($fields) ? $errorCssClass : '';
}

/**
 * Add css class when link is currently active
 *
 * @param array|string $path Single path or array of paths
 * @param string $activeClass Default is 'active'
 * @return string
 */
function active_link($path, $activeClass = 'active')
{
    return Request::path() == $path ? $activeClass : '';
}

function active_name($name, $activeClass = 'active')
{
    return request()->route()->named($name) ? $activeClass : '';
}

if (!function_exists('escapeLike')) {

    function escapeLike(?string $string, $expPercent = false): string
    {
        if ($expPercent) {
            $search = ['\\', '_', '&', '|'];
            $replace = ['\\\\', '\_', '\&', '\|'];
        } else {
            $search = ['\\', '%', '_', '&', '|'];
            $replace = ['\\\\', '\%', '\_', '\&', '\|'];
        }

        return str_replace($search, $replace, $string);
    }
}

if (!function_exists('isValidMail')) {

    function isValidMail(string $email, $verify_email = 0)
    {
        $invalidDomain = config('mail.invalid_domain');

        return (!in_array(substr($email, stripos($email, '@') + 1), $invalidDomain) && $verify_email == 0);
    }
}

if (!function_exists('http_build_url_query')) {

    function http_build_url_query(string $url, array $queryData = [])
    {
        if (!empty($queryData)) {
            $url .= '?' . http_build_query($queryData);
        }

        return $url;
    }
}

if (!function_exists('mempty')) {

    function mempty(...$arguments)
    {
        foreach($arguments as $argument) {
            if(empty($argument)) {
                return true;
            }
        }

        return false;
    }
}

if (!function_exists('convert_to_slug')) {

    function convert_to_slug(string $str, $exist = false)
    {
        $slug = Str::slug(preg_replace('/\W+/', ' ', Str::ascii($str)));

        if ($exist) {
            $slug = $slug . '-' . str_random(5);
        }

        return $slug;
    }
}

if (!function_exists('countRating')) {

    function countRating($count, $view) {
        return ceil($count + config('rating.count.start') + $view/config('rating.count.value'));
    }
}

if (!function_exists('pointRating')) {

    function pointRating($count, $rating)
    {
        $point = config('rating.point.value');

        foreach ($rating as $rate) {
            $point += $rate->rating_number;
        }

        return round($point / (config('rating.point.start') + $count), 1, PHP_ROUND_HALF_ODD);
    }
}

if (!function_exists('percentRating')) {

    function percentRating($avgRate, $total = 5) {
        $avgRate = $avgRate > 0 ? $avgRate : config('rating.avg_default');

        return round($avgRate / $total, 1) * 100;
    }
}

if (!function_exists('ratingCount')) {

    function ratingCount($count, $max = 50) {
        return $count > 0 ? $count : round($max * 0.2, 0); // default: 20% of max
    }
}

if (!function_exists('assetThumnail')) {

    function assetThumnail($originalFilePath, $size = [], $imgDefault = 'images/course-df-thumbnail.jpg')
    {
        if ($originalFilePath) {
            $originalFilePath = ltrim($originalFilePath, '/');

            if (!empty($size)) {
                $lastPositionOfDot = strrpos($originalFilePath, '.');

                if ($lastPositionOfDot === false) {
                    return asset(sprintf('%s_%sx%s', $originalFilePath, $size[0], $size[1]));
                }

                $extension = substr($originalFilePath, $lastPositionOfDot);
                $startName = substr($originalFilePath, 0, $lastPositionOfDot);

                return asset(sprintf('%s_%sx%s%s', $startName, $size[0], $size[1], $extension));
            }

            return asset($originalFilePath);
        }

        return asset_cdn($imgDefault);
    }
}

if (!function_exists('rand_float')) {

    function rand_float($st_num = 0, $end_num = 1, $mul = 1000000)
    {
        if ($st_num > $end_num) return false;

        return mt_rand($st_num * $mul, $end_num * $mul) / $mul;
    }
}

if (!function_exists('replace_base_url')) {

    function replace_base_url($url, $replacements = 'https://khoahoc.vietjack.com')
    {
        $patterns = '/^https?:\/\/[^\/]+/i';

        return preg_replace($patterns, $replacements, $url);
    }
}

if (!function_exists('renderUrlVideo')) {

    function renderUrlVideo($payload = [], $mediaChecking = 0)
    {
        $raw_url = $payload['raw_url'] ?: '';

        if ($mediaChecking == 2) {
            $urlArr = explode('/', $raw_url);
            $payload['raw_url'] = '/backup/old_video/'. end($urlArr);
        }

        return rtrim(config('app.video_url'), '/') . '/media/video/' . JWT::encode($payload, config('app.video_url_secret')) . '/video.m3u8';
    }
}

if (!function_exists('dateFormat')) {
    function dateFormat(string $date, string $format = 'd/m/Y H:i'): string
    {
        return date_format(new DateTime($date), $format);
    }
}

function html_clean($content = '')
{
    return preg_replace('/[\s\\\]+/mu', ' ', html_entity_decode(strip_tags($content)));
}

function text_clean($str = '')
{
    return preg_replace('/[\s\\\]+/mu', ' ', $str);
}

function getIdVideoYoutube($url)
{
    $regex = '/(?im)\b(?:https?:\/\/)?(?:w{3}\.)?youtu(?:be)?\.(?:com|be)\/(?:(?:\??v=?i?=?\/?)|watch\?vi?=|watch\?.*?&v=|embed\/|)([A-Z0-9_-]{11})\S*(?=\s|$)/';

    preg_match($regex, $url, $matches);

    return $matches[1] ?? '';
}

function removeEmptyTags($html_replace)
{
    $pattern = "/<[^\/>]*>([\s]?)*<\/[^>]*>/";

    return preg_replace($pattern, '', $html_replace);
}

function get_between($input, $start, $end)
{
    $substr = substr($input, strpos($input, $start), (strlen($input) - strpos($input, $end) - strlen($end))*(-1));

    return $substr;
}

function remove_font_family($content, $replacements = '', $removeMore = false)
{
    if ($removeMore) {
        // remove comment html
        $content = preg_replace('/(?=<!--)([\s\S]*?)-->/', '', $content);
        // remove some style atribute
        $content = preg_replace('/(font-family|line-height|font-size|text-indent).+?;/', $replacements, $content);
    } else {
        $content = preg_replace('/font-family.+?;/', $replacements, $content);
    }

    $content = str_replace('<span style="mso-spacerun: yes;">&nbsp; </span>', ' ', $content);
    $content = preg_replace('/(<[^>]+) data-sheets-value=".*?"/i', '$1', $content);
    $content = preg_replace('/(<[^>]+) data-sheets-userformat=".*?"/i', '$1', $content);

    return $content;
}

function getDomainWithScheme(string $url): ?string
{
    if (preg_match('#^https?://[^/]+#i', $url, $m)) {
        return $m[0]; // http://domain hoặc https://domain
    }
    return null;
}

function replaceStr($str, $from = '', $to = '')
{
    if ($from && $to) {
        $str = str_replace($from, $to, $str);
    }

    return $str;
}

function replacePath($str, $path, $newPAth)
{
    $domainFromPath = getDomainWithScheme($path);

    if ($domainFromPath) {
        $str = replaceStr($str, $domainFromPath, '');
    }

    return replaceStr($str, parse_url($path, PHP_URL_PATH), $newPAth);
}

function str_to_slug($str, $limit = 70)
{
    $slug = Str::limit(Str::slug(preg_replace('/\W+/', ' ', Str::ascii($str))), $limit, '');

    return trim($slug, '-');
}

function is_valid_url($url)
{
    return filter_var($url, FILTER_VALIDATE_URL) !== false;
}

function handle_banner($uploadedPath)
{
    if ($uploadedPath) {
        if (is_valid_url($uploadedPath)) {
            return $uploadedPath;
        } else {
            return asset($uploadedPath);
        }
    }

    return asset_cdn('images/course-df-thumbnail.jpg');
}

function asset_cdn($path, $useCdn = true, $cdnRoot = null)
{
    if (is_valid_url($path)) {
        return $path;
    }

    if ($useCdn) {
        $cdnRoot = $cdnRoot ?: config('app.asset_cdn');

        return rtrim($cdnRoot, '/') . '/' . trim($path, '/');
    }

    return asset($path);
}

function mix_cdn($path, $useCdn = true, $cdnRoot = null)
{
    if (is_valid_url($path)) {
        return $path;
    }

    $mixPath = mix($path);

    if ($useCdn) {
        $cdnRoot = $cdnRoot ?: config('app.mix_cdn');

        return rtrim($cdnRoot, '/') . '/' . trim($mixPath, '/');
    }

    return $mixPath;
}

function selected_select2_values($selectedValues)
{
    return is_array($selectedValues) ? implode(',', $selectedValues) : $selectedValues;
}

function selected_select_value($selectedKey, $selectedValue)
{
    return request()->get($selectedKey) == $selectedValue ? 'selected' : '';
}

function hexToRgba($hex, $opacity = 1) {
    $hex = ltrim($hex, '#');
    $length = strlen($hex);

    // Xử lý mã hex dạng #RGB hoặc #RRGGBB
    $rgb = array_map('hexdec', str_split($length === 6 ? $hex : str_repeat($hex, 2), $length === 6 ? 2 : 1));

    return sprintf('rgba(%d, %d, %d, %.2f)', $rgb[0], $rgb[1], $rgb[2], max(0, min(1, $opacity)));
}

function escapeElastic(?string $string)
{
    $regex = "/[\\+\\-\\=\\&\\|\\!\\(\\)\\{\\}\\[\\]\\^\\\"\\~\\*\\<\\>\\?\\:\\\\\\/]/";

    return preg_replace($regex, addslashes('\\$0'), $string);
}

function formatDateString($dateString, $format = 'd/m/Y')
{
    return date($format, strtotime($dateString));
}
