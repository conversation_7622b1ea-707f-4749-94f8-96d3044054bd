<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Routing\Router;

$adminRoute = config('base.admin_route');

//Backend
Route::group(['prefix' => $adminRoute, 'middleware' => 'check-role-manage'], function (Router $router) {
    $router->get('dashboard/index', 'DashboardController@getIndex')
        ->name('nqadmin::dashboard.index.get')
        ->middleware('dashboard-redirect', 'permission:access_dashboard');
});

//Frontend
Route::group(['namespace' => 'Frontend'], function (Router $router) {
    $router->get('/', 'HomeController@getIndex')
        ->name('front.home.index.get');

    $router->get('landing-{name_landing}', 'HomeController@landing')
        ->name('get-landing')
        ->middleware('landing-page-mapping');
    $router->post('landing-{name_landing}', 'HomeController@postLanding')
        ->name('post-landing');

    $router->get('get-hotexam', 'HomeController@getHotExams')
        ->name('front.home.hotexam.get');

    $router->group(['prefix' => 'khoa-hoc'], function () use ($router) {
        $router->get('/', 'HomeController@searchCourse')
            ->name('front.home.search-course');
        $router->get('{slug}', 'HomeController@getCourseByClass')
            ->name('front.classlevel.index.get');
        $router->get('{classSlug}/mon-{subjectSlug}', 'HomeController@getCourseByClassSubject')
            ->name('front.subject.index.get');
    });

    $router->group(['prefix' => 'trac-nghiem'], function () use ($router) {
        $router->get('/', 'HomeController@searchTracNghiem')
            ->name('front.home.search-choice');
        $router->get('{slug}', 'HomeController@searchTracNghiemByClass')
            ->name('front.home.search-choice.class');
        $router->get('{classSlug}/mon-{subjectSlug}', 'HomeController@getExamByClassSubject')
            ->name('home.exam.class-subject');
        $router->get('{classSlug}/mon-{subjectSlug}/{chapterSlug}', 'HomeController@getExamByClassSubjectChapter')
            ->name('home.exam.class-subject-chapter');

        $router->get('{classSlug}/mon-{subjectSlug}/{chapterSlug}/{lessonSlug}', 'HomeController@getExamByClassSubjectChapterLesson')
            ->name('home.exam.class-subject-chapter-lesson');
    });

    $router->group(['prefix' => 'bai-giang'], function () use ($router) {
        $router->get('{classSlug}/mon-{subjectSlug}', 'HomeController@getVideoByClassSubject')
            ->name('home.exam.lesson');
    });

    $router->get('cau-hoi', 'HomeController@quizQuestionIndex')->name('cau-hoi-trac-nghiem');
    $router->get('cau-hoi/mon-{slug1}/{slug2}', 'HomeController@searchQuizQuestion')
        ->name('cau-hoi-trac-nghiem.search');

    $router->group(['prefix' => 'tu-luan'], function () use ($router) {
        $router->get('/', 'HomeController@essayQuestionIndex')
            ->name('cau-hoi-tu-luan');
        $router->get('mon-{slug}', 'HomeController@searchEssayBySubject')
            ->name('front.home.essay.subject');
        $router->get('mon-{slug1}/{slug2}', 'HomeController@searchEssayQuestion')
            ->name('cau-hoi-tu-luan.search');
        $router->get('{slug}', 'HomeController@searchEssayByClass')
            ->name('cau-hoi-tu-luan.class.search');
    });

    $router->get('dang-ky-vip', 'HomeController@vipRegister')
        ->name('dang-ky-vip');
    $router->get('kich-hoat-vip', 'HomeController@vipActive')
        ->name('kich-hoat-vip');

    $router->get('p-{key}', 'HomeController@page')->name('get-page');

    $router->post('comments/upload-image', 'ImageController@upload')
        ->name('comments.upload-image.post')->middleware('auth');
    $router->post('comments/remove-image', 'ImageController@remove')
        ->name('comments.remove-image.post')->middleware('auth');

    $router->get('diem-chuan', 'AdmissionController@benchMarkIndex')
        ->name('front.admission.post-benchmark-index');
    $router->get('dap-an-de-thi', 'AdmissionController@examIndex')
        ->name('front.admission.post-exam-index');

    $router->group(['prefix' => 'thong-tin-tuyen-sinh'], function () use ($router) {
        $router->get('/', 'AdmissionController@index')
            ->name('front.admission.index');

        $router->get('{level}/khu-vuc-{area}', 'AdmissionController@getListByArea')
            ->name('front.admission.area');

        $router->get('school/{id}/{school}', 'AdmissionController@getAdmissionOld')
            ->name('front.admission.school-old');

        $router->get('tin-tuc/{id}/{slug}', 'AdmissionController@getAdmissionPostOld')
            ->name('front.admission.post-old');
    });

    $router->get('school/{id}/{school}', 'AdmissionController@getAdmission')
        ->name('front.admission.school');

    $router->get('tuyen-sinh/{id}/{slug}', 'AdmissionController@getAdmissionPost')
        ->name('front.admission.post');

    $router->get('download/admission/{id}', 'AdmissionController@downloadDocument')
        ->name('front.admission.download');

    $router->group(['prefix' => 'livestreams'], function () use ($router) {
        $router->get('/', 'LivestreamController@index')
            ->name('front.livestream.index');
        $router->get('/{slug}/{id?}', 'LivestreamController@show')
            ->name('front.livestream.show')
            ->middleware('view-livestream-lesson');
    });
});

Route::get('{upload}/{mail}/thumbnails/{fileName?}', function ($folder = null, $mail = null, $fileName = null) {
    return response()->file(public_path('/adminux/img/course-df-thumbnail.jpg'));
})->where('upload', 'upload.*?');

Route::get('/search/query', 'SearchController@search')->name('search.query');
Route::get('/admission/search/query', 'SearchController@admissionSearch')->name('admission.search.query');

Route::group(['prefix' => '/suggest'], function () {
    Route::get('/classes', 'SuggestController@classesSuggest')->name('suggest.classes');
    Route::get('/subjects', 'SuggestController@subjectsSuggest')->name('suggest.subjects');
    Route::get('/courses', 'SuggestController@coursesSuggest')->name('suggest.courses');
    Route::get('/books', 'SuggestController@booksSuggest')->name('suggest.books');
    Route::get('/chapters', 'SuggestController@chaptersSuggest')->name('suggest.chapters');
    Route::get('/lessons', 'SuggestController@lessonsSuggest')->name('suggest.lessons');
    Route::get('/teachers', 'SuggestController@teachersSuggest')->name('suggest.teachers');
    Route::get('/users', 'SuggestController@usersSuggest')->name('suggest.users');
    Route::get('/livestreams', 'SuggestController@livestreamsSuggest')->name('suggest.livestreams');
    Route::get('/categories', 'SuggestController@categoriesSuggest')->name('suggest.categories');

    Route::group(['prefix' => '/question'], function () {
        Route::get('/classes', 'SuggestController@classesForQuestion')->name('suggest.question.classes');
        Route::get('/subjects', 'SuggestController@subjectsForQuestion')->name('suggest.question.subjects');
        Route::get('/books', 'SuggestController@booksForQuestion')->name('suggest.question.books');
        Route::get('/chapters', 'SuggestController@chaptersForQuestion')->name('suggest.question.chapters');
        Route::get('/lessons', 'SuggestController@lessonsForQuestion')->name('suggest.question.lessons');
        Route::get('/courses', 'SuggestController@coursesForQuestion')->name('suggest.question.courses');
    });
});

Route::get('install-app-vietjack', function () {
    return view('nqadmin-dashboard::page.install-app');
});
