<?php

namespace Base\Services;

use ClassLevel\Models\ClassLevel;
use Subject\Models\Subject;
use Book\Models\Book;
use Season\Models\Season;
use Course\Models\CourseCurriculumItems;
use Base\Traits\Cacheable;
use DB;

class CommonService
{
    use Cacheable;

    protected function getTTL()
    {
        return 43200; // minutes - 30 day
    }

    public function getClassesCourse()
    {
        return $this->remember(function() {
            $classCourseData = DB::select("
                SELECT
                    vjc_classlevel.id as classlevel_id,
                    vjc_classlevel.name as classlevel_name,
                    vjc_classlevel.slug as classlevel_slug,
                    vjc_subject.id as subject_id,
                    vjc_subject.name as subject_name,
                    vjc_subject.slug as subject_slug,
                    vjc_subject.icon as subject_icon
                FROM
                    vjc_subject
                        JOIN
                    vjc_course ON vjc_subject.id = vjc_course.subject
                        AND vjc_course.type = 'normal'
                        AND vjc_course.status = 'active'
                        JOIN
                    vjc_classlevel ON vjc_classlevel.id = vjc_course.classlevel
                GROUP BY vjc_classlevel.id , vjc_subject.id
                ORDER BY vjc_classlevel.id DESC;
            ");

            $groupSubjects = [];
            $groupClasses = [];

            foreach ($classCourseData as $classCourse) {
                $dataArr = get_object_vars($classCourse);
                $classlevelArr = [
                    'id' => $dataArr['classlevel_id'],
                    'name' => $dataArr['classlevel_name'],
                    'slug' => $dataArr['classlevel_slug'],
                ];
                $subjectArr = [
                    'id' => $dataArr['subject_id'],
                    'name' => $dataArr['subject_name'],
                    'slug' => $dataArr['subject_slug'],
                    'icon' => $dataArr['subject_icon'],
                ];
                $groupSubjects[$dataArr['classlevel_id']][] = $subjectArr;
                $groupClasses[$dataArr['classlevel_id']] = $classlevelArr;
            }

            return [
                'classes' => $groupClasses,
                'subjects' => $groupSubjects,
            ];
        });
    }

    public function getClassesExam()
    {
        return $this->remember(function() {
            // 4 query (tổng time query ~ 250ms) + tự xử lý nhóm dữ liệu
            $classes = ClassLevel::select(
                    'id',
                    'name',
                    'slug',
                    'index',
                    'group',
                    'color',
                    'thumbnail'
                )
                ->whereExists(function ($query) {
                    $query->select(DB::raw(1))
                          ->from('course')
                          ->whereRaw('vjc_course.classlevel = vjc_classlevel.id')
                          ->where('course.type', 'test')
                          ->where('course.status', 'active');
                })
                ->orderBy('index')
                ->orderBy('id', 'desc')
                ->get();

            if ($classes->isEmpty()) {
                return [];
            }

            $books = Book::select(
                    'books.id',
                    'books.name',
                    'books.type',
                    'books.index',
                    'books.hidden_home',
                    'books.icon',
                    'book_classlevel.class_id'
                )
                ->where('type', '<>', Book::TYPE_SHOW_ALL)
                ->where('hidden_home', 0)
                ->join('book_classlevel', 'books.id', '=', 'book_classlevel.book_id')
                ->whereIn('book_classlevel.class_id', $classes->pluck('id'))
                ->orderBy('books.index', 'asc')
                ->get()
                ->groupBy('class_id');
            $subjectsInBook = Subject::select(
                    'subject.id',
                    'subject.name',
                    'subject.slug',
                    'subject.index_tollbar',
                    'subject.thumbnail',
                    'season.class_id',
                    'season.book_id'
                )
                ->join('season', 'subject.id', '=', 'season.subject_id')
                ->whereNotNull('season.book_id')
                ->whereIn('season.class_id', $classes->pluck('id'))
                ->groupBy('subject.id', 'season.class_id', 'season.book_id')
                ->orderBy('subject.index_tollbar')
                ->orderBy('subject.id')
                ->get()
                ->groupBy(function ($item) {
                    return $item->class_id . '-' . $item->book_id;
                });
            $subjectsNotBook = Subject::select(
                    'subject.id',
                    'subject.name',
                    'subject.slug',
                    'subject.index_tollbar',
                    'subject.thumbnail',
                    'course.classlevel'
                )
                ->join('course', 'subject.id', '=', 'course.subject')
                ->where('course.type', 'test')
                ->where('course.status', 'active')
                ->whereNull('course.book_id')
                ->whereIn('course.classlevel', $classes->pluck('id'))
                ->groupBy('subject.id', 'course.classlevel')
                ->orderBy('subject.index_tollbar')
                ->orderBy('subject.id')
                ->get()
                ->groupBy('classlevel');

            // tự xử lý nhóm dữ liệu
            foreach ($classes as $classdata) {
                $classdata->subjects_without_book = $subjectsNotBook->get($classdata->id, collect());

                $classBooks = $books->get($classdata->id, collect());
                $classdata->books = $classBooks;

                foreach ($classdata->books as $book) {
                    $key = $classdata->id . '-' . $book->id;
                    $book->subjects = $subjectsInBook->get($key, collect());
                }
            }

            return [
                'classes' => $classes,
            ];
        });
    }

    public function getClassesExam2($classSelected = null)
    {
        $query = ClassLevel::select(
                'classlevel.id',
                'classlevel.slug',
                'classlevel.name',
                'classlevel.index',
                'classlevel.type',
                'classlevel.color',
                'classlevel.icon',
                'classlevel.seo_description'
            )
            ->where('classlevel.type', 1)
            ->withCount('books')
            ->orderBy('classlevel.index', 'asc')
            ->orderBy('classlevel.id', 'desc');

        if ($classSelected) {
            $query->where('id', $classSelected->id);
        }

        return $query->get();
    }

    public function getSubjectsExam($classSelected = null)
    {
        $query = Subject::select(
                'subject.id',
                'subject.name',
                'subject.slug',
                'subject.thumbnail',
                'subject.icon',
                'course.classlevel as classlevel_id',
                DB::raw('COUNT(vjc_course.book_id) as books_count'),
                DB::raw('SUM(vjc_course.curriculums_count) as exam_count'),
                DB::raw('COALESCE(SUM(vjc_course.related_lectures_count), 0) AS lecture_count')
            )
            ->join('course', function ($join) use ($classSelected) {
                $join->on('subject.id', '=', 'course.subject')
                    // ->where('course.status', 'active')
                    ->whereIn('course.type', ['test', 'essay']);

                    if ($classSelected) {
                        $join->where('course.classlevel', $classSelected->id);
                    }
            })
            ->groupBy('course.classlevel', 'subject.id')
            ->orderBy('subject.id');

        if (!empty($classSelected->exam_subjects)) {
            $query->whereIn('subject.id', $classSelected->exam_subjects)
                ->orderByRaw('FIELD(vjc_subject.id, ' . implode(',', $classSelected->exam_subjects) . ')');
        } else {
            $query->where('subject.show_tollbar', 1)
                ->orderBy('subject.index_tollbar');
        }

        return $query->get()->where('exam_count', '>', 0);
    }

    public function hotExams($classId, $subjectId = null)
    {
        if (!$classId) {
            return collect([]);
        }

        return $this->remember(function() use ($classId, $subjectId) {
            return CourseCurriculumItems::select(
                    'course_curriculum_items.id',
                    'course_curriculum_items.type',
                    'course_curriculum_items.status',
                    'course_curriculum_items.name',
                    'course_curriculum_items.daily_view',
                    'course_curriculum_items.count2week',
                    'course_curriculum_items.updated_at',
                    'course.slug',
                    'course.view',
                    'course.classlevel',
                    'course.subject'
                )
                ->join('course', function ($join) use ($classId, $subjectId) {
                    $join->on('course.id', '=', 'course_curriculum_items.course_id')
                        ->where('course.classlevel', $classId);

                    if ($subjectId) {
                        $join->where('course.subject', $subjectId);
                    }
                })
                ->where('course_curriculum_items.type', 'test')
                ->where('course_curriculum_items.status', 'active')
                ->orderBy('course_curriculum_items.count2week', 'DESC')
                ->orderBy('course_curriculum_items.id', 'DESC')
                ->limit(8)
                ->get()
                ->loadCount('curriculumQuestions');
        }, 4320);  // minutes - 3 day
    }

    public function getSeasonsWithCache($class_id, $subject_id, $type, $book_ids = [])
    {
        return $this->remember(function() use ($class_id, $subject_id, $type, $book_ids) {
            $seasonsQuery = Season::join('course', function($join) use ($type) {
                    $join->on('course.season_id', '=', 'season.id')
                        ->where('course.status', 'active');

                    if ($type == 'essay') {
                        $join->where('type', 'essay');
                    } else {
                        $join->where('type', 'test');
                    }
                })
                ->select(
                    'season.id',
                    'season.name',
                    'season.slug',
                    'season.class_id',
                    'season.subject_id',
                    'season.book_id',
                    'season.index',
                    DB::raw('COALESCE(SUM(vjc_course.related_lectures_count), 0) AS lecture_count')
                )
                ->where('season.class_id', $class_id)
                ->where('season.subject_id', $subject_id)
                ->with([
                    'lessons' => function ($query) use ($type) {
                        $query->select(
                            'id',
                            'name',
                            'slug',
                            'season_id',
                            'index',
                            'status'
                        )
                        ->with([
                            'courses' => function ($que) use ($type) {
                                $que->select(
                                        'id',
                                        'name',
                                        'slug',
                                        'status',
                                        'type',
                                        'classlevel',
                                        'subject',
                                        'book_id',
                                        'season_id',
                                        'lesson_id',
                                        'sort',
                                        'related_lectures_count'
                                    )
                                    ->where('status', 'active')
                                    ->orderBy('sort')
                                    ->orderBy('id');

                                    if ($type == 'essay') {
                                        $que->where('type', 'essay')
                                            ->withCount(['curriculumEssay as exam_count' => function($query) {
                                                $query->where('status', 'active');
                                            }]);
                                    } else {
                                        $que->where('type', 'test')
                                            ->withCount(['curriculumTest as exam_count' => function($query) {
                                                $query->where('status', 'active');
                                            }]);
                                            // ->withCount(['curriculumLecture as lecture_count' => function($query) {
                                            //     $query->where('status', 'active');
                                            // }]);
                                    }
                                }
                        ])
                        ->orderBy('index')
                        ->orderBy('id');
                    },
                    'courses' => function ($q) use ($type) {
                        $q->select(
                                'id',
                                'name',
                                'slug',
                                'status',
                                'type',
                                'classlevel',
                                'subject',
                                'book_id',
                                'season_id',
                                'lesson_id',
                                'sort',
                                'related_lectures_count'
                            )
                            ->whereNull('lesson_id')
                            ->where('status', 'active')
                            ->orderBy('sort')
                            ->orderBy('id');

                        if ($type == 'essay') {
                            $q->where('type', 'essay')
                                ->withCount(['curriculumEssay as exam_count' => function($query) {
                                    $query->where('status', 'active');
                                }]);
                        } else {
                            $q->where('type', 'test')
                                ->withCount(['curriculumTest as exam_count' => function($query) {
                                    $query->where('status', 'active');
                                }]);
                                // ->withCount(['curriculumLecture as lecture_count' => function($query) {
                                //     $query->where('status', 'active');
                                // }]);
                        }
                    }
                ])
                ->groupBy('season.id')
                ->orderBy('season.index')
                ->orderBy('season.id');

            if ($type == 'essay') {
                $seasonsQuery->withCount(['curriculumEssay as exam_count' => function($query) {
                    $query->where('course_curriculum_items.status', 'active');
                }]);
            } else {
                $seasonsQuery->withCount(['curriculumTest as exam_count' => function($query) {
                    $query->where('course_curriculum_items.status', 'active');
                }]);
            }

            if (empty($book_ids)) {
                $seasonsQuery->whereNull('season.book_id');
            } else {
                $seasonsQuery->whereIn('season.book_id', $book_ids);
            }

            return $seasonsQuery->get();
        }, 2880); // minutes - 2 day
    }
}
