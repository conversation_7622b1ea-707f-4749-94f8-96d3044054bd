<?php

namespace Base\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use ClassLevel\Models\ClassLevel;
use Livestream\Models\Livestream;
use Subject\Models\Subject;
use Season\Models\Season;
use Season\Models\Lesson;
use Course\Models\Course;
use Users\Models\Users;
use Acl\Models\Role;
use Book\Models\Book;
use Category\Models\Category;

class SuggestController extends Controller
{
    private function dashboardRequest()
    {
        $previousRequest = request()->create(url()->previous());
        $segment = $previousRequest->segment(1);

        return $segment == config('base.admin_route');
    }

    protected function select2SuggestWithPaging(Request $request, $query, $nameColumn, $otherColumns = [], $resultCallback = null)
    {
        $textColumn = $nameColumn;

        if ($selectedIds = $request->input('selectedValues')) {
            $selectedIds = explode(',', $selectedIds);
            $query = $query->whereIn('id', $selectedIds);
        } else {
            $q = escapeLike($request->input('q'));
            $query = $query->where($textColumn, 'like', '%' . $q . '%');

            if (!empty($otherColumns)) {
                foreach ($otherColumns as $col) {
                    $query = $query->orWhere($col, 'like', '%' . $q . '%');
                }
            }
        }

        $paginatedResults = $query->select("$textColumn as text", 'id');

        if ($otherColumns) {
            $paginatedResults->addSelect($otherColumns);
        }

        $paginatedResults = $paginatedResults->paginate();
        $results = $paginatedResults->items();

        return response()->json([
            'results' => is_callable($resultCallback) ? $resultCallback($results) : $results,
            'pagination' => [
                'more' => $paginatedResults->hasMorePages(),
            ],
        ]);
    }

    public function categoriesSuggest(Request $request)
    {
        return $this->select2SuggestWithPaging(
            $request,
            Category::whereNull('parent_id'),
            'name',
            [],
            null
        );
    }

    public function classesSuggest(Request $request)
    {
        return $this->select2SuggestWithPaging(
            $request,
            ClassLevel::whereType(ClassLevel::IS_CLASS)
                ->orderBy('index', 'ASC')
                ->orderBy('id', 'ASC'),
            'name',
            [],
            null
        );
    }

    public function booksSuggest(Request $request)
    {
        $query = Book::query();

        if ($request->class_id) {
            $query->whereHas('classlevels', function ($q) use ($request) {
                $q->where('classlevel.id', $request->class_id);
            });
        }

        return $this->select2SuggestWithPaging(
            $request,
            $query,
            'name',
            [],
            null
        );
    }

    public function subjectsSuggest(Request $request)
    {
        return $this->select2SuggestWithPaging(
            $request,
            Subject::query(),
            'name',
            [],
            null
        );
    }

    public function teachersSuggest(Request $request)
    {
        $query = Users::where('position', 'giao_vien');

        return $this->select2SuggestWithPaging(
            $request,
            $query,
            'first_name',
            [],
            null
        );
    }

    public function usersSuggest(Request $request)
    {
        if ($request->role && !$request->selectedValues) {
            $role = Role::where('name', $request->role)->first();
            $query = $role->users();
        } else {
            $query = Users::query();
        }

        return $this->select2SuggestWithPaging(
            $request,
            $query,
            'first_name',
            ['email', 'phone'],
            null
        );
    }

    public function coursesSuggest(Request $request)
    {
        $query = Course::query();

        if ($request->type) {
            if ($request->type == 'normal') {
                $query->whereIn('type', ['normal', 'video']);
            } else {
                $query->where('type', $request->type);
            }
        }

        if ($request->author) {
            $query->where('author', $request->author);
        }

        if ($request->origin_id) {
            $query->where('id', '<>', $request->origin_id);
        }

        return $this->select2SuggestWithPaging(
            $request,
            $query,
            'name',
            [],
            null
        );
    }

    public function chaptersSuggest(Request $request)
    {
        $query = Season::query();

        if ($request->class_id) {
            $query->where('class_id', $request->class_id);
        }

        if ($request->subject_id) {
            $query->where('subject_id', $request->subject_id);
        }

        if ($request->book_id) {
            $query->where('book_id', $request->book_id);
        } elseif (!$request->selectedValues) {
            $query->whereNull('book_id');
        }

        return $this->select2SuggestWithPaging(
            $request,
            $query,
            'name',
            [],
            null
        );
    }

    public function lessonsSuggest(Request $request)
    {
        $query = Lesson::query();

        if ($request->season_id) {
            $query->where('season_id', $request->season_id);
        }

        return $this->select2SuggestWithPaging(
            $request,
            $query,
            'name',
            [],
            null
        );
    }

    public function livestreamsSuggest(Request $request)
    {
        $query = Livestream::query();

        if ($request->author_id) {
            $query->where('author_id', $request->author_id);
        }

        if ($request->subject_id) {
            $query->where('subject_id', $request->subject_id);
        }

        return $this->select2SuggestWithPaging(
            $request,
            $query,
            'name',
            ['price'],
            null
        );
    }

    public function classesForQuestion(Request $request)
    {
        $q = escapeLike($request->input('q'));
        $results = ClassLevel::whereHas('course', function ($q) {
                $q->where('type', 'test')
                    ->where('status', 'active');
            })
            ->where('name', 'like', '%' . $q . '%')
            ->select('name as text', 'id')
            ->orderBy('index', 'ASC')
            ->orderBy('id', 'ASC')
            ->get();

        return response()->json([
            'results' => $results,
        ]);
    }

    public function subjectsForQuestion(Request $request)
    {
        $q = escapeLike($request->input('q'));
        $results = Subject::whereHas('course', function ($q) use ($request) {
                if ($request->class_id) {
                    $q->where('classlevel', $request->class_id);
                }
                $q->where('type', 'test')
                    ->where('status', 'active');
            })
            ->where('name', 'like', '%' . $q . '%')
            ->select('name as text', 'id')
            ->orderBy('id', 'asc')
            ->get();

        return response()->json([
            'results' => $results,
        ]);
    }

    public function booksForQuestion(Request $request)
    {
        $results = Book::where('type', Book::TYPE_DEFAULT)
            ->withCount(['course as test_count' => function($q) use ($request) {
                if ($request->class_id) {
                    $q->where('classlevel', $request->class_id);
                }

                if ($request->subject_id) {
                    $q->where('subject', $request->subject_id);
                }

                $q->where('type', 'test')
                    ->where('status', 'active');
            }])
            ->withCount(['course as essay_count' => function($q) use ($request) {
                if ($request->class_id) {
                    $q->where('classlevel', $request->class_id);
                }

                if ($request->subject_id) {
                    $q->where('subject', $request->subject_id);
                }

                $q->where('type', 'essay')
                    ->where('status', 'active');
            }])
            ->orderBy('index', 'asc')
            ->get()
            ->filter(function($book) {
                return $book->test_count > 0 || $book->essay_count > 0;
            })->map(function($book) {
                return [
                    'id' => $book->id,
                    'text' => $book->name,
                    'test_count' => $book->test_count,
                    'essay_count' => $book->essay_count,
                ];
            })->toArray();

        return response()->json([
            'results' => array_values($results), // reset key để ko bị chuyển thành object ở js
        ]);
    }

    public function chaptersForQuestion(Request $request)
    {
        $q = escapeLike($request->input('q'));
        $isDashboardRequest = $this->dashboardRequest();
        $results = Season::whereHas('courses', function ($q) use ($request, $isDashboardRequest) {
                if ($request->class_id) {
                    $q->where('classlevel', $request->class_id);
                }

                if ($request->subject_id) {
                    $q->where('subject', $request->subject_id);
                }

                if ($request->book_id) {
                    $q->where('book_id', $request->book_id);
                } elseif (!$request->selectedValues) {
                    $q->whereNull('book_id');
                }

                if (!$isDashboardRequest) {
                    $q->where('type', 'test')->where('status', 'active');
                }
            })
            ->where('name', 'like', '%' . $q . '%')
            ->select('name as text', 'id')
            ->orderBy('id', 'asc')
            ->get();

        return response()->json([
            'results' => array_merge([['text' => 'Tất cả các chương', 'id' => 0]], $results->toArray()),
        ]);
    }

    public function lessonsForQuestion(Request $request)
    {
        $q = escapeLike($request->input('q'));
        $results = Lesson::where('season_id', $request->chapter_id)
            ->where('name', 'like', '%' . $q . '%')
            ->select('name as text', 'id')
            ->orderBy('id', 'asc')
            ->get();

        return response()->json([
            'results' => array_merge([['text' => 'Tất cả các bài', 'id' => 0]], $results->toArray()),
        ]);
    }

    public function coursesForQuestion(Request $request)
    {
        $limit = 9;
        $initItem = (int) $request->input('init', $limit);
        $page = max(1, (int) $request->input('page', 1));

        if ($page === 1) {
            $offset = 0;
        } else {
            $offset = $initItem + ($page - 2) * $limit;
        }

        $query = Course::select(
                'id',
                'name',
                'slug',
                'type',
                'status',
                'sort',
                'classlevel',
                'subject',
                'book_id',
                'season_id',
                'lesson_id'
            )
            ->where('status', 'active')
            ->orderBy('sort')
            ->orderBy('id');

        if ($request->type == 'essay') {
            $query->where('type', 'essay')
                ->withCount(['curriculumEssay as exam_count' => function($que) {
                    $que->where('status', 'active');
                }]);
        } else {
            $query->where('type', 'test')
                ->withCount(['curriculumTest as exam_count' => function($que) {
                    $que->where('status', 'active');
                }]);
        }

        if ($request->chapter) {
            $query->where('season_id', $request->chapter);
        }

        if ($request->lesson) {
            $query->where('lesson_id', $request->lesson);
        }

        $courses = $query->offset($offset)
            ->limit($limit)
            ->get();

        if ($request->sidebar == 1) {
            return response()->json([
                'html' => view('nqadmin-course::frontend.lecture.tests.partials.lsidebar-list', [
                    'sidebarCourses' => $courses,
                    'ml' => $request->ml ?: 25,
                ])->render(),
                'action' => route('suggest.question.courses', [
                    'type' => $request->type,
                    'chapter' => $request->chapter,
                    'lesson' => $request->lesson,
                    'page' => $page + 1,
                    'init' => $initItem,
                    'sidebar' => 1,
                    'ml' => $request->ml ?: 25,
                ]),
                'hasMore' => $courses->count() >= $limit,
            ]);
        }

        return response()->json([
            'html' => view('nqadmin-course::frontend.course.exam.partials.exam-tree-list', [
                'courses' => $courses,
                'page' => $page,
            ])->render(),
            'action' => route('suggest.question.courses', [
                'type' => $request->type,
                'chapter' => $request->chapter,
                'lesson' => $request->lesson,
                'page' => $page + 1,
                'init' => $initItem,
            ]),
            'hasMore' => $courses->count() >= $limit,
        ]);
    }
}
