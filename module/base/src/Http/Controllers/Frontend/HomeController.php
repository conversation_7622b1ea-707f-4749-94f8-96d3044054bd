<?php

namespace Base\Http\Controllers\Frontend;

use App\Http\Controllers\Controller as BaseController;
use Illuminate\Http\Request;
use Rating\Models\Rating;
use Base\Models\TrackingUtmLandingDataPhone;
use Base\Models\TrackingViewLanding;
use Base\Models\TrackingViewLandingData;
use Base\Models\TrackingViewLandingDataPhone;
use ClassLevel\Models\ClassLevel;
use Subject\Models\Subject;
use Book\Models\Book;
use Course\Models\Course;
use Course\Models\CourseCurriculumItems;
use Course\Models\LectureTest;
use Level\Models\Level;
use MultipleChoices\Models\Question;
use Users\Models\Users;
use Setting\Models\Banner;
use Setting\Models\Page;
use Media\Models\Media;
use Season\Models\Season;
use Season\Models\Lesson;
use Cart\Models\OrderPackage;
use Base\Models\TrackingView;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\View;
use Base\Requests\LandingRequest;
use Base\Services\CommonService;
use Base\Traits\Cacheable;
use Jenssegers\Agent\Facades\Agent;
use Carbon\Carbon;

class HomeController extends BaseController
{
    use Cacheable;

    protected $commonService;

    public function __construct(CommonService $commonService)
    {
        if (config('app.page_type') == 'video') {
            abort(403, 'Unauthorized action.');
        }

        $this->commonService = $commonService;
    }

    protected function getTTL()
    {
        return 14400; // minutes - 10 day
    }

    private function getDataHomeWithCache2()
    {
        return $this->remember(function() {
            // old
            // $examClasses = $this->commonService->getClassesExam();
            // $examCountArr = DB::table('course_curriculum_items as cci')
            //     ->join('course as c', function($join) {
            //         $join->on('cci.course_id', '=', 'c.id')
            //             ->where('c.type', 'test')
            //             ->where('c.status', 'active')
            //             ->whereNotNull('c.classlevel')
            //             ->whereNotNull('c.subject');
            //     })
            //     ->select(
            //         'c.classlevel',
            //         'c.subject',
            //         'c.book_id',
            //         DB::raw('COUNT(*) as exam_count')
            //     )
            //     ->where('cci.type', 'test')
            //     ->groupBy('c.classlevel', 'c.subject', 'c.book_id')
            //     ->get();
            $classes = $this->commonService->getClassesExam2();
            $subjects = $this->commonService->getSubjectsExam();
            $topCourses = Course::where('status', 'active')
                ->where('type', 'normal')
                ->where('trending', 1)
                ->orderBy('trending_index', 'desc')
                ->with('getLdp', 'owner')
                ->limit(10)
                ->get();

            return [
                // 'examClasses' => $examClasses['classes'] ?? [],
                // 'examCountArr' => $examCountArr,
                'classes' => $classes,
                'subjects' => $subjects,
                'topCourses' => $topCourses,
            ];
        });
    }

    public function getIndex()
    {
        $dataHomes = $this->getDataHomeWithCache2();
        $latestTrackingView = null;

        if (Auth::check()) {
            $latestTrackingView = TrackingView::where('user_id', Auth::id())
                ->orderBy('updated_at', 'DESC')
                ->limit(4)
                ->with([
                    'exam' => function ($query) {
                        $query->select(
                            'id',
                            'name',
                            'course_id',
                            'daily_view',
                            'count2week'
                        )
                        ->withCount('getAllQuestion')
                        ->with(['getCourse' => function($query) {
                            $query->select('id', 'slug', 'view');
                        }]);
                    },
                    'question' => function ($query) {
                        $query->select(
                            'id',
                            'title',
                            'slug',
                            'content',
                            'view'
                        );
                    }
                ])
                ->get();
        }

        return view('nqadmin-dashboard::frontend.index', [
            'dataHomes' => $dataHomes,
            'latestTrackingView' => $latestTrackingView,
        ]);
    }

    public function getHotExams(Request $request)
    {
        $hotExams = $request->classId ? $this->commonService->hotExams($request->classId) : collect([]);
 
        return response()->json([
            'html' => view('nqadmin-dashboard::frontend.components.hometop.hotexams', [ 'hotExams' => $hotExams ])->render(),
        ]);
    }

    public function vipRegister(Request $request)
    {
        $utm_tracking = [
            'utm_source',
            'utm_medium',
            'utm_campaign',
            'utm_term',
            'utm_content',
        ];

        $utm_data = [];

        foreach ($utm_tracking as $utm) {
            $queryValue = $request->get($utm);

            if ($queryValue) {
                $utm_data[$utm] = $queryValue;
            }
        }

        if (!empty($utm_data)) {
            $newUtmJson = json_encode($utm_data);
            $oldUtmJson = $request->cookie('_vj_vip_');

            if ($oldUtmJson !== $newUtmJson) {
                cookie()->queue(cookie('_vj_vip_', $newUtmJson, 60 * 24 * 14)); // 14 ngày
            }
        }

        return view('nqadmin-dashboard::frontend.vip-package');
    }

    public function vipActive(Request $request)
    {
        if (!Auth::check()) {
            return redirect(route('dang-ky-vip'))->with('error', 'Yêu cầu đăng nhập!');
        }

        if ($request->code) {
            $checkCode = OrderPackage::where('type', OrderPackage::TYPE_NEW)
                ->where('code', $request->code)
                ->first();

            if ($checkCode) {
                $checkCode->update([
                    'customer' => Auth::id(),
                    'status' => OrderPackage::ACTIVE,
                    'type' => OrderPackage::TYPE_USED,
                    'expired_at' => Carbon::now()->addMonths($checkCode->date_duration),
                ]);

                return redirect('/')->with('info', 'Tài khoản VIP của bạn đã được kích hoạt.');
            }
        }

        return redirect(route('dang-ky-vip'))->with('error', 'Mã code không hợp lệ!');
    }

    public function searchCourse(Request $request)
    {
        $classCourseData = $this->commonService->getClassesCourse();
        $courses = Course::where('type', 'normal')
            ->where('status', 'active')
            ->where('sort', '>=', 0)
            ->with('getClassLevel', 'getLdp')
            ->withCount([
                'getCurriculum as section_count' => function ($query) {
                    $query->where('type', 'section')
                        ->where('status', 'active');
                },
                'media as total_duration' => function($q){
                    $q->select(DB::raw('COALESCE(SUM(`vjc_media`.`duration`), 0)'));
                },
            ])
            ->orderBy('classlevel', 'DESC')
            ->orderBy('sort', 'DESC')
            ->paginate(20);

        return view('nqadmin-dashboard::frontend.courses-index', [
            'classCourseData' => $classCourseData,
            'courses' => $courses,
        ]);
    }

    public function getCourseByClass($slug, Request $request)
    {
        $classLevel = ClassLevel::where('slug', $slug)->firstOrFail();
        $classCourseData = $this->commonService->getClassesCourse();

        $courses = Course::where('type', 'normal')
            ->where('status', 'active')
            ->where('classlevel', $classLevel->id)
            ->where('sort', '>=', 0)
            ->with('getClassLevel', 'getLdp')
            ->withCount([
                'getCurriculum as section_count' => function ($query) {
                    $query->where('type', 'section')
                        ->where('status', 'active');
                },
                'media as total_duration' => function($q){
                    $q->select(DB::raw('COALESCE(SUM(`vjc_media`.`duration`), 0)'));
                },
            ])
            ->orderBy('classlevel', 'DESC')
            ->orderBy('sort', 'DESC')
            ->paginate(20);

        return view('nqadmin-dashboard::frontend.courses-index', [
            'classLevel' => $classLevel,
            'classCourseData' => $classCourseData,
            'courses' => $courses,
        ]);
    }

    public function getCourseByClassSubject($classSlug, $subjectSlug, Request $request)
    {
        $classLevel = ClassLevel::where('slug', $classSlug)->firstOrFail();
        $subject = Subject::where('slug', $subjectSlug)->firstOrFail();
        $classCourseData = $this->commonService->getClassesCourse();

        $courses = Course::where('type', 'normal')
            ->where('status', 'active')
            ->where('classlevel', $classLevel->id)
            ->where('subject', $subject->id)
            ->where('sort', '>=', 0)
            ->with('getClassLevel', 'getLdp')
            ->withCount([
                'getCurriculum as section_count' => function ($query) {
                    $query->where('type', 'section')
                        ->where('status', 'active');
                },
                'media as total_duration' => function($q){
                    $q->select(DB::raw('COALESCE(SUM(`vjc_media`.`duration`), 0)'));
                },
            ])
            ->orderBy('classlevel', 'DESC')
            ->orderBy('sort', 'DESC')
            ->paginate(20);

        return view('nqadmin-dashboard::frontend.courses-index', [
            'classLevel' => $classLevel,
            'subject' => $subject,
            'classCourseData' => $classCourseData,
            'courses' => $courses,
        ]);
    }

    public function searchTracNghiem()
    {
        $classes = $this->commonService->getClassesExam2();
        $subjects = $this->commonService->getSubjectsExam();

        return view('nqadmin-course::frontend.course.exam.index', [
            'classes' => $classes,
            'subjects' => $subjects,
        ]);
    }

    public function quizQuestionIndex(Request $request)
    {
        $classes = ClassLevel::where('status', 'active')
            ->with('subject')
            ->orderBy('index', 'asc')
            ->orderBy('id', 'desc')
            ->get();

        $questions = Question::with(['getLecture.getCourse' => function ($query) {
                $query->with('getSubject', 'getClassLevel');
            }]);
            // ->where('type', 'test');

        if (Auth::check() && Auth::user()->canEditQuestion() &&
            in_array($request->sort, ['id', 'view', 'count2week', 'updated_at'])) {
            $questions->orderBy($request->sort, 'desc');
        } else {
            $questions->orderBy('id', 'desc');
        }

        $questions = $questions->paginate(50);

        return view('nqadmin-dashboard::page.cau-hoi-trac-nghiem', compact('classes', 'questions'));
    }

    public function searchQuizQuestion($slug1, $slug2, Request $request)
    {
        $subject_main = Subject::where('slug', $slug1)->firstOrFail();
        $class_main = ClassLevel::where('slug', $slug2)->firstOrFail();
        $classes = ClassLevel::where('status', 'active')
            ->with('subject')
            ->orderBy('index', 'asc')
            ->orderBy('id', 'desc')
            ->get();

        // $questions = Question::whereHas('getLecture', function ($query) use ($subject_main, $class_main) {
        //     $query->whereHas('getCourse', function ($que) use ($subject_main, $class_main) {
        //         $que->where('classlevel', $class_main->id)
        //             ->where('subject', $subject_main->id)
        //             ->where('type', 'test')
        //             ->where('status', 'active');
        //     });
        // })->with(['getLecture.getCourse' => function ($query) {
        //     $query->with('getSubject', 'getClassLevel');
        // }]);

        $questions = Question::select('questions.*')
            ->join('course_curriculum_items', 'course_curriculum_items.id', '=', 'questions.curriculum_item')
            ->join('course', function ($join) use ($subject_main, $class_main) {
                $join->on('course.id', '=', 'course_curriculum_items.course_id')
                    ->where('course.type', 'test')
                    ->where('course.classlevel', $class_main->id)
                    ->where('course.subject', $subject_main->id)
                    ->where('course.status', 'active');
            })->with(['getLecture.getCourse' => function ($query) {
                $query->with('getSubject', 'getClassLevel');
            }]);

        if (Auth::check() && Auth::user()->canEditQuestion() &&
            in_array($request->sort, ['id', 'view', 'count2week', 'updated_at'])) {
            $questions->orderBy($request->sort, 'desc');
        } else {
            $questions->orderBy('id', 'desc');
        }

        $questions = $questions->paginate(50);

        return view('nqadmin-dashboard::page.cau-hoi-trac-nghiem', compact(
            'classes',
            'questions',
            'subject_main',
            'class_main'
        ));
    }

    public function essayQuestionIndex(Request $request)
    {
        $questions = Question::where('type', 'essay')
            ->with(['getLecture.getCourse' => function ($query) {
                $query->with('getSubject', 'getClassLevel');
            }])
            ->orderBy('id', 'DESC')
            ->paginate(50);

        return view('nqadmin-dashboard::page.cau-hoi-tu-luan', compact('questions'));
    }

    public function searchEssayQuestion($slug1, $slug2, Request $request)
    {
        $subject_main = Subject::where('slug', $slug1)->first();
        $class_main = ClassLevel::where('slug', $slug2)->first();

        if (empty($subject_main) || empty($class_main)) {
            return redirect(route('cau-hoi-tu-luan'))->with('success', 'Tài liệu đang được cập nhật!');
        }

        $questions = Question::select('questions.*')
            ->join('course_curriculum_items', 'course_curriculum_items.id', '=', 'questions.curriculum_item')
            ->join('course', function ($join) use ($subject_main, $class_main) {
                $join->on('course.id', '=', 'course_curriculum_items.course_id')
                    ->where('course.type', 'essay')
                    ->where('course.classlevel', $class_main->id)
                    ->where('course.subject', $subject_main->id);
            })->with(['getLecture.getCourse' => function ($query) {
                $query->with('getSubject', 'getClassLevel');
            }])
            ->orderBy('id', 'DESC')
            ->paginate(50);

        return view('nqadmin-dashboard::page.cau-hoi-tu-luan', compact('questions'));
    }

    public function searchEssayByClass($slug)
    {
        $class_main = ClassLevel::where('slug', $slug)->first();

        if ($class_main) {
            $questions = Question::select('questions.*')
                ->join('course_curriculum_items', 'course_curriculum_items.id', '=', 'questions.curriculum_item')
                ->join('course', function ($join) use ($class_main) {
                    $join->on('course.id', '=', 'course_curriculum_items.course_id')
                        ->where('course.type', 'essay')
                        ->where('course.classlevel', $class_main->id);
                })->with(['getLecture.getCourse' => function ($query) {
                    $query->with('getSubject', 'getClassLevel');
                }])
                ->orderBy('id', 'desc')
                ->paginate(50);

            return view('nqadmin-dashboard::page.cau-hoi-tu-luan', compact('questions'));
        } else {
            return redirect(route('cau-hoi-tu-luan'));
        }
    }

    public function searchEssayBySubject($slug)
    {
        $subject_main = Subject::where('slug', $slug)->first();

        if ($subject_main) {
            $questions = Question::select('questions.*')
                ->join('course_curriculum_items', 'course_curriculum_items.id', '=', 'questions.curriculum_item')
                ->join('course', function ($join) use ($subject_main) {
                    $join->on('course.id', '=', 'course_curriculum_items.course_id')
                        ->where('course.type', 'essay')
                        ->where('course.subject', $subject_main->id);
                })->with(['getLecture.getCourse' => function ($query) {
                    $query->with('getSubject', 'getClassLevel');
                }])
                ->orderBy('id', 'desc')
                ->paginate(50);

            return view('nqadmin-dashboard::page.cau-hoi-tu-luan', compact('questions'));
        } else {
            return redirect(route('cau-hoi-tu-luan'));
        }
    }

    public function landing($name_landing, Request $request)
    {
        $utm_tracking = [
            'utm_source',
            'utm_medium',
            'utm_campaign',
            'utm_term',
            'utm_content',
        ];

        foreach ($utm_tracking as $utm) {
            if ($request->$utm) session([$utm => $request->$utm]);
        }

        $check_view = true;
        $check = TrackingViewLanding::where(function ($query) {
                if (Auth::check()) {
                    $query->where('user_id', Auth::id());
                }
            })->where('landing_id', $name_landing)
            ->where('ip', $request->ip())
            ->orderBy('id', 'desc')
            ->first();

        if ((!empty($check) && (time() - strtotime($check->created_at) < 1800))) {
            $check_view = false;
        }

        try {
            if ($check_view) {
                DB::beginTransaction();

                TrackingViewLanding::create([
                    'user_id' => Auth::check() ? Auth::id() : null,
                    'landing_id' => $name_landing,
                    'ip' => $request->ip(),
                ]);

                if (Auth::check()) {
                    $data = [
                        'user_id' => Auth::id(),
                        'landing_id' => $name_landing,
                        'name' => Auth::user()->first_name,
                        'phone' => Auth::user()->phone,
                        'email' => Auth::user()->email,
                        'utm_source' => session('utm_source'),
                        'utm_medium' => session('utm_medium'),
                        'utm_campaign' => session('utm_campaign'),
                        'utm_term' => session('utm_term'),
                        'utm_content' => session('utm_content'),
                    ];
                    TrackingViewLandingData::firstOrCreate($data);

                    $dataPhone = [
                        'phone' => Auth::user()->phone,
                    ];
                    $dataPhone = TrackingViewLandingDataPhone::firstOrCreate($dataPhone);

                    // if (!empty(session('utm_source')) && !empty(session('utm_campaign'))) {
                    //     $utmData = [
                    //         'phone' => Auth::user()->phone,
                    //         'utm_source' => session('utm_source'),
                    //         'utm_medium' => session('utm_medium'),
                    //         'utm_campaign' => session('utm_campaign'),
                    //         'utm_term' => session('utm_term'),
                    //         'utm_content' => session('utm_content'),
                    //     ];
                    //     $utmData = TrackingUtmLandingDataPhone::firstOrCreate($utmData);
                    // }
                }

                DB::commit();
            }

            if (View::exists('nqadmin-dashboard::landing.' . $name_landing)) {
                return view('nqadmin-dashboard::landing.' . $name_landing);
            } else {
                return redirect(route('front.home.index.get'));
            }
        } catch (\Exception $e) {
            DB::rollback();
            report($e);

            return redirect(route('front.home.index.get'));
        }
    }

    public function postLanding($name_landing, LandingRequest $request)
    {
        DB::beginTransaction();

        try {
            if (Auth::check() && !empty($request->phone) && empty(Auth::user()->phone)) {
                Auth::user()->phone = $request->phone;
                Auth::user()->save();
            }

            $data = [
                'user_id' => Auth::check() ? Auth::id() : null,
                'landing_id' => $name_landing,
                'name' => Auth::check() ? Auth::user()->first_name : $request->name,
                'phone' => $request->phone,
                'email' => Auth::check() ? Auth::user()->email : $request->email,
                'class_level' => $request->class_level,
                'position' => $request->position,
                'dob' => $request->dob,
                'level' => $request->level,
                'delivery_address' => $request->delivery_address ?? null,
                'utm_source' => session('utm_source'),
                'utm_medium' => session('utm_medium'),
                'utm_campaign' => session('utm_campaign'),
                'utm_term' => session('utm_term'),
                'utm_content' => session('utm_content'),
            ];

            $data = TrackingViewLandingData::firstOrCreate($data);
            $data->updated_at = date('Y-m-d H:i:s');
            $data->save();

            $dataPhone = [
                'phone' => $request->phone,
            ];
            $dataPhone = TrackingViewLandingDataPhone::firstOrCreate($dataPhone);
            $dataPhone->updated_at = date('Y-m-d H:i:s');
            $dataPhone->save();

            if (!mempty(session('utm_source'), session('utm_medium'), session('utm_campaign'))) {
                $dataTracking = TrackingUtmLandingDataPhone::firstOrCreate([
                    'phone' => $request->phone,
                    'utm_source' => session('utm_source'),
                    'utm_medium' => session('utm_medium'),
                    'utm_campaign' => session('utm_campaign'),
                    'utm_term' => session('utm_term'),
                    'utm_content' => session('utm_content'),
                ]);
                $dataTracking->update(['phone' => $request->phone, 'updated_at' => date('Y-m-d H:i:s')]);
            }

            DB::commit();

            if ($request->expectsJson()) {
                return response()->json(200);
            }

            $classNumber = preg_replace('/[^0-9]/', '', $request->class_level) ?:
                config('course.landing_page_redirect.' . $name_landing . '.class-number'); // get class number
            $urlRedirect = url(config('course.landing_page_redirect.' . $name_landing . '.url-prefix'), $classNumber ? ('Lop-' . $classNumber) : null);

            return redirect(is_string($urlRedirect) ? $urlRedirect : '/' )
                ->with('success', 'Hệ thống đã ghi nhận thông tin của bạn. Xin chân thành cảm ơn!');
        } catch (\Exception $e) {
            DB::rollback();
            report($e);

            return redirect(route('front.home.index.get'));
        }
    }

    public function page($key, Request $request)
    {
        $page = Page::where('key', $key)->first();

        if ($page) {
            return view('nqadmin-dashboard::page.page', compact('page'));
        } else {
            return redirect(route('front.home.index.get'));
        }
    }

    private function examCount($classId, $subjectId, $bookIds = [])
    {
        return $this->remember(function() use ($classId, $subjectId, $bookIds) {
            return CourseCurriculumItems::select([
                    DB::raw('SUM(
                        CASE WHEN vjc_course.type = "test"
                             THEN vjc_course.related_lectures_count
                             ELSE 0
                        END
                    ) AS test_video_count'),
                    DB::raw('SUM(
                        CASE WHEN vjc_course.type = "essay"
                             THEN vjc_course.related_lectures_count
                             ELSE 0
                        END
                    ) AS essay_video_count'),
                    DB::raw('SUM(CASE WHEN vjc_course_curriculum_items.type = "test" THEN 1 ELSE 0 END) as test_count'),
                    DB::raw('SUM(CASE WHEN vjc_course_curriculum_items.type = "essay" THEN 1 ELSE 0 END) as essay_count')
                ])
                ->join('course', function ($join) use ($classId, $subjectId, $bookIds) {
                    $join->on('course.id', '=', 'course_curriculum_items.course_id')
                        ->where('course.status', 'active')
                        ->where('course.classlevel', $classId)
                        ->where('course.subject', $subjectId);

                    if (!empty($bookIds)) {
                        $join->whereIn('course.book_id', $bookIds);
                    }
                })
                ->where('course_curriculum_items.status', 'active')
                ->first();
        });
    }

    public function getVideoByClassSubject($classSlug, $subjectSlug, Request $request)
    {
        $class = ClassLevel::whereSlug($classSlug)->first();
        $subject = Subject::whereSlug($subjectSlug)->first();

        if ($class && $subject) {
            $book = null;
            $bookIds = [];

            if ($request->book) {
                $book = Book::find($request->book);

                if ($book) {
                    // có các chương nhập để show ở tất cả các bộ sách.
                    $bookIds = Book::where('type', Book::TYPE_SHOW_ALL)->pluck('id')->toArray();
                    // tính cả những chương thuộc bộ sách hiện tại và các chương thuộc tất cả bộ sách
                    array_push($bookIds, $book->id);
                }
            }

            $examCounts = $this->examCount($class->id, $subject->id, $bookIds);
            $type = $examCounts->test_video_count > $examCounts->essay_video_count ? 'test' : 'essay';
            $seasons = $this->commonService->getSeasonsWithCache($class->id, $subject->id, $type, $bookIds);

            if ($seasons->isEmpty()) {
                return redirect()->route('home.exam.class-subject', [
                    'classSlug' => $class->slug, 
                    'subjectSlug' => $subject->slug, 
                    'book' => $request->book,
                ]);
            }

            $seasons->loadMissing([
                'courses.relatedLectures' => function ($q) {
                    $q->select(
                        'course_curriculum_items.id',
                        'course_curriculum_items.name',
                        'course_curriculum_items.status',
                        'course_curriculum_items.type'
                    );
                },
                'lessons.courses.relatedLectures' => function ($q) {
                    $q->select(
                        'course_curriculum_items.id',
                        'course_curriculum_items.name',
                        'course_curriculum_items.status',
                        'course_curriculum_items.type'
                    );
                },
                'courses.relatedLectures.contentMedia',
                'lessons.courses.relatedLectures.contentMedia',
            ]);

            $subjects = $this->commonService->getSubjectsExam($class);
            $hotExams = $this->commonService->hotExams($class->id, $subject->id);

            return view('nqadmin-course::frontend.course.lecture.show' ,compact(
                'class',
                'subjects', 
                'subject',
                'book',
                'hotExams',
                'examCounts',
                'seasons'
            ));
        }

        abort(404);
    }

    public function getExamByClassSubject($classSlug, $subjectSlug, Request $request)
    {
        $type = $request->type;

        if ($type && !in_array($type, ['test', 'essay'])) {
            return redirect()->route('front.home.search-choice')->with('success', 'Đề thi đang được cập nhật!');
        }

        $type = $type ?: 'test';
        $class = ClassLevel::whereSlug($classSlug)->first();
        $subject = Subject::whereSlug($subjectSlug)->first();

        if ($class && $subject) {
            $book = null;
            $bookIds = [];

            if ($request->book) {
                $book = Book::find($request->book);

                if ($book) {
                    // có các chương nhập để show ở tất cả các bộ sách.
                    $bookIds = Book::where('type', Book::TYPE_SHOW_ALL)->pluck('id')->toArray();
                    // tính cả những chương thuộc bộ sách hiện tại và các chương thuộc tất cả bộ sách
                    array_push($bookIds, $book->id);
                }
            }

            $examCounts = $this->examCount($class->id, $subject->id, $bookIds);

            if ($type == 'test' && $examCounts->test_count == 0 && $examCounts->essay_count > 0) {
                return redirect()->route('home.exam.class-subject', [
                    'classSlug' => $classSlug,
                    'subjectSlug' => $subjectSlug,
                    'type' => 'essay',
                    'book' => $request->book,
                ]);
            }

            $subjects = $this->commonService->getSubjectsExam($class);
            $seasons = $this->commonService->getSeasonsWithCache($class->id, $subject->id, $type, $bookIds);
            $hotExams = $this->commonService->hotExams($class->id, $subject->id);

            if ($seasons->isEmpty()) {
                $coursesQuery = Course::where('classlevel', $class->id)
                    ->where('subject', $subject->id)
                    ->where('status', 'active')
                    ->orderBy('sort', 'desc')
                    ->orderBy('id', 'desc');

                if ($book) {
                    $coursesQuery->where('book_id', $book->id);
                }

                if ($type == 'essay') {
                    $coursesQuery->where('type', 'essay')
                        ->withCount(['curriculumEssay as exam_count' => function($query) {
                            $query->where('status', 'active');
                        }]);
                } else {
                    $coursesQuery->where('type', 'test')
                        ->withCount(['curriculumTest as exam_count' => function($query) {
                            $query->where('status', 'active');
                        }]);
                        // ->withCount(['curriculumLecture as lecture_count' => function($query) {
                        //     $query->where('status', 'active');
                        // }]);
                }

                $courses = $coursesQuery->paginate(15);

                return view('nqadmin-course::frontend.course.exam.detail', compact(
                    'class',
                    'subjects',
                    'subject',
                    'courses',
                    'book',
                    'examCounts',
                    'hotExams'
                ));
            }

            return view('nqadmin-course::frontend.course.exam.show', compact(
                'class',
                'subjects',
                'subject',
                'seasons',
                'book',
                'examCounts',
                'hotExams'
            ));
        }

        abort(404);
    }

    public function getExamByClassSubjectChapter(
        $classSlug,
        $subjectSlug,
        $chapterSlug,
        Request $request
    )
    {
        $type = $request->type;

        if ($type && !in_array($type, ['test', 'essay'])) {
            return redirect()->route('front.home.search-choice')->with('success', 'Đề thi đang được cập nhật!');
        }

        $type = $type ?: 'test';
        $class = ClassLevel::whereSlug($classSlug)->first();
        $subject = Subject::whereSlug($subjectSlug)->first();
        $chapter = Season::whereSlug($chapterSlug)->first();

        if ($class && $subject && $chapter) {
            if ($chapter->class_id !== $class->id || $chapter->subject_id !== $subject->id) {
                return redirect(route('front.home.search-choice'))->with('success', 'Đề thi đang được cập nhật!');
            }

            $book = null;
            $bookIds = [];

            if ($request->book) {
                $book = Book::find($request->book);

                if ($book) {
                    // có các chương nhập để show ở tất cả các bộ sách.
                    $bookIds = Book::where('type', Book::TYPE_SHOW_ALL)->pluck('id')->toArray();
                    // tính cả những chương thuộc bộ sách hiện tại và các chương thuộc tất cả bộ sách
                    array_push($bookIds, $book->id);
                }
            }

            $seasons = $this->commonService->getSeasonsWithCache($class->id, $subject->id, $type, $bookIds);
            [$match, $others] = $seasons->partition(function($item) use ($chapter) {
                return $item->id === $chapter->id;
            });

            if ($match->isEmpty()) {
                return redirect(route('front.home.search-choice'))->with('success', 'Đề thi đang được cập nhật!');
            }

            $seasons = $match->concat($others)->values();
            $subjects = $this->commonService->getSubjectsExam($class);
            $test_count = 'SUM(CASE WHEN vjc_course_curriculum_items.type = "test" THEN 1 ELSE 0 END) as test_count';
            $essay_count = 'SUM(CASE WHEN vjc_course_curriculum_items.type = "essay" THEN 1 ELSE 0 END) as essay_count';

            if ($type == 'test') {
                $test_count = 'SUM(CASE WHEN vjc_course_curriculum_items.type = "test" AND vjc_course.season_id = "' . $chapter->id  . '" THEN 1 ELSE 0 END) as test_count';
            }

            if ($type == 'essay') {
                $essay_count = 'SUM(CASE WHEN vjc_course_curriculum_items.type = "essay" AND vjc_course.season_id = "' . $chapter->id  . '" THEN 1 ELSE 0 END) as essay_count';
            }

            $examCounts = CourseCurriculumItems::select([
                    DB::raw($test_count),
                    DB::raw($essay_count)
                ])
                ->join('course', function ($join) use ($class, $subject, $bookIds) {
                    $join->on('course.id', '=', 'course_curriculum_items.course_id')
                        ->where('course.status', 'active')
                        ->where('course.classlevel', $class->id)
                        ->where('course.subject', $subject->id);

                    if (!empty($bookIds)) {
                        $join->whereIn('course.book_id', $bookIds);
                    }
                })
                ->where('course_curriculum_items.status', 'active')
                ->first();
            $hotExams = $this->commonService->hotExams($class->id, $subject->id);

            return view('nqadmin-course::frontend.course.exam.show', compact(
                'class',
                'subjects',
                'subject',
                'seasons',
                'book',
                'examCounts',
                'hotExams'
            ));
        }

        abort(404);
    }

    public function getExamByClassSubjectChapterLesson(
        $classSlug,
        $subjectSlug,
        $chapterSlug,
        $lessonSlug,
        Request $request
    )
    {
        $type = $request->type;

        if ($type && !in_array($type, ['test', 'essay'])) {
            return redirect()->route('front.home.search-choice');
        }

        $type = $type ?: 'test';
        $class = ClassLevel::whereSlug($classSlug)->first();
        $subject = Subject::whereSlug($subjectSlug)->first();
        $chapter = Season::whereSlug($chapterSlug)->first();
        $lesson = Lesson::whereSlug($lessonSlug)->first();
        // $lessonQuery = Lesson::whereSlug($lessonSlug);

        // if ($type == 'essay') {
        //     $lessonQuery->withCount(['curriculumEssay as exam_count' => function($query) {
        //         $query->where('course_curriculum_items.status', 'active');
        //     }]);
        // } else {
        //     $lessonQuery->withCount(['curriculumTest as exam_count' => function($query) {
        //         $query->where('course_curriculum_items.status', 'active');
        //     }]);
        // }

        // $lesson = $lessonQuery->first();

        if ($class && $subject && $chapter && $lesson) {
            $book = null;
            $bookIds = [];

            if ($request->book) {
                $book = Book::find($request->book);

                if ($book) {
                    // có các chương nhập để show ở tất cả các bộ sách.
                    $bookIds = Book::where('type', Book::TYPE_SHOW_ALL)->pluck('id')->toArray();
                    // tính cả những chương thuộc bộ sách hiện tại và các chương thuộc tất cả bộ sách
                    array_push($bookIds, $book->id);
                }
            }

            $subjects = $this->commonService->getSubjectsExam($class);
            $test_count = 'SUM(CASE WHEN vjc_course_curriculum_items.type = "test" THEN 1 ELSE 0 END) as test_count';
            $essay_count = 'SUM(CASE WHEN vjc_course_curriculum_items.type = "essay" THEN 1 ELSE 0 END) as essay_count';

            if (!$type || $type == 'test') {
                $test_count = 'SUM(CASE WHEN vjc_course_curriculum_items.type = "test" AND vjc_course.season_id = "' . $chapter->id  . '" AND vjc_course.lesson_id = "' . $lesson->id  . '" THEN 1 ELSE 0 END) as test_count';
            }

            if ($type == 'essay') {
                $essay_count = 'SUM(CASE WHEN vjc_course_curriculum_items.type = "essay" AND vjc_course.season_id = "' . $chapter->id  . '" AND vjc_course.lesson_id = "' . $lesson->id  . '" THEN 1 ELSE 0 END) as essay_count';
            }

            $examCounts = CourseCurriculumItems::select([
                    DB::raw($test_count),
                    DB::raw($essay_count)
                ])
                ->join('course', function ($join) use ($class, $subject, $bookIds) {
                    $join->on('course.id', '=', 'course_curriculum_items.course_id')
                        ->where('course.status', 'active')
                        ->where('course.classlevel', $class->id)
                        ->where('course.subject', $subject->id);

                    if (!empty($bookIds)) {
                        $join->whereIn('course.book_id', $bookIds);
                    }
                })
                ->where('course_curriculum_items.status', 'active')
                ->first();
            $coursesQuery = Course::where('classlevel', $class->id)
                ->where('subject', $subject->id)
                ->where('season_id', $chapter->id)
                ->where('lesson_id', $lesson->id)
                ->where('status', 'active')
                ->orderBy('count2week', 'desc');

            if ($book) {
                $coursesQuery->where('book_id', $book->id);
            }

            if ($type == 'essay') {
                $coursesQuery->where('type', 'essay')
                    ->withCount(['curriculumEssay as exam_count' => function($query) {
                        $query->where('status', 'active');
                    }]);
            } else {
                $coursesQuery->where('type', 'test')
                    ->withCount(['curriculumTest as exam_count' => function($query) {
                        $query->where('status', 'active');
                    }]);
            }

            $courses = $coursesQuery->paginate(20);

            if ($courses->total() == 0) {
                return redirect(route('front.home.search-choice'))->with('success', 'Đề thi đang được cập nhật!');
            }

            return view('nqadmin-course::frontend.course.exam.detail', compact(
                'class',
                'subjects',
                'subject',
                'chapter',
                'lesson',
                'courses',
                'book',
                'examCounts'
            ));
        }

        return redirect(route('front.home.search-choice'))->with('success', 'Đề thi đang được cập nhật!');
    }

    public function searchTracNghiemByClass($slug, Request $request)
    {
        $type = $request->type ?? 'test';
        $bookId = $request->book;
        $classQuery = ClassLevel::whereHas('course', function ($q) use ($type) {
                if ($type == 'all') {
                    $q->whereIn('type', ['test', 'essay']);
                } else if ($type == 'essay') {
                    $q->where('type', 'essay');
                } else {
                    $q->where('type', 'test');
                }

                $q->where('status', 'active');
            })
            ->whereSlug($slug);

        if ($bookId) {
            $classQuery->whereHas('books', function ($q) use ($bookId) {
                $q->where('books.id', $bookId);
            });
        }

        $class = $classQuery->first();

        if (!$class) {
            abort(404);
        }

        $classId = $class->id;
        $subject = Subject::select('subject.*')
            ->join('course', function ($join) use ($class, $type, $bookId) {
                $join->on('subject.id', '=', 'course.subject')
                    ->where('course.status', 'active')
                    ->where('course.classlevel', $class->id);

                if ($type == 'all') {
                    $join->whereIn('course.type', ['test', 'essay']);
                } else if ($type == 'essay') {
                    $join->where('course.type', 'essay');
                } else {
                    $join->where('course.type', 'test');
                }

                if ($bookId) {
                    $join->where('course.book_id', $bookId);
                }
            })
            ->groupBy('subject.id')
            ->orderBy('subject.id')
            ->first();

        if ($subject) {
            return redirect()->route('home.exam.class-subject', [
                'classSlug' => $class->slug,
                'subjectSlug' => $subject->slug,
                'type' => $type,
                'book' => $bookId,
            ]);
        }

        return redirect()->route('front.home.search-choice')->with('success', 'Tài liệu đang được cập nhật!');
    }
}
