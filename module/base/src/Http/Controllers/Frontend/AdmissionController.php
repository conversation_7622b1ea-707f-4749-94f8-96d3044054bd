<?php

namespace Base\Http\Controllers\Frontend;

use App\Http\Controllers\Controller as BaseController;
use Admission\Models\AdmissionArea;
use Admission\Models\AdmissionLevel;
use Admission\Models\AdmissionPost;
use Admission\Models\Admission;
use Admission\Services\AdmissionService;
use Base\Services\File\FileService;
use Illuminate\Http\Request;
use Base\Traits\Cacheable;
use DB;

class AdmissionController extends BaseController
{
    use Cacheable;

    protected $admissionService;

    public function __construct(AdmissionService $admissionService)
    {
        $this->admissionService = $admissionService;
    }

    protected function getTTL()
    {
        return 14400; // minutes - 10 day
    }

    public function index()
    {
        $admissionLevel = AdmissionLevel::where('status', AdmissionLevel::ACTIVE)
            ->get();
        $admissionArea = AdmissionArea::where('status', AdmissionArea::ACTIVE)
            ->get();
        $newAdmissions = Admission::where('status', Admission::ACTIVE)
            ->orderBy('id', 'desc')
            ->paginate(20);

        return view('nqadmin-admission::frontend.index', compact('admissionLevel', 'admissionArea', 'newAdmissions'));
    }

    public function getListByArea($levelSlug, $areaSlug, Request $request)
    {
        $admissionArea = AdmissionArea::where('slug', $areaSlug)->first();
        $admissionLevel = AdmissionLevel::where('slug', $levelSlug)->first();

        if ($admissionArea && $admissionLevel) {
            $admission = Admission::select('id', 'name', 'code', 'slug', 'status', 'admission_area_id', 'admission_level_id')
                ->where('admission_area_id', $admissionArea->id)
                ->where('status', Admission::ACTIVE)
                ->where('admission_level_id', $admissionLevel->id)
                ->paginate(100);
        } else {
            abort(404);
        }

        $admissionLevelList = AdmissionLevel::where('status', AdmissionLevel::ACTIVE)->get();
        $admissionAreaList = AdmissionArea::where('status', AdmissionArea::ACTIVE)->get();

        return view('nqadmin-admission::frontend.show', compact(
            'admission',
            'admissionArea',
            'admissionLevel',
            'admissionAreaList',
            'admissionLevelList'
        ));
    }

    public function getAdmissionOld($id, $school, Request $request)
    {
        return redirect()->route('front.admission.school', ['id' => $id, 'school' => $school]);
    }

    public function getAdmission($id, $school, Request $request)
    {
        $admission = Admission::find($id);

        if (!$admission) {
            return redirect()->route('front.admission.index')->with('error', 'Thông tin đang cập nhật');
        }

        if ($school != $admission->slug) {
            return redirect()->route('front.admission.school', ['id' => $admission->id, 'school' => $admission->slug]);
        }

        $admission->timestamps = false;
        $admission->update(['view' => $admission->view + 1]);
        $admission->load('admissionLevel', 'admissionArea');

        $topPosts = $this->getTopPostsOfAdmission($admission);
        $relatedSchool = Admission::select([
                'id',
                'name',
                'slug',
                'status',
                'code',
                'thumbnail',
                'banner',
                'admission_area_id',
                'admission_level_id',
            ])
            ->where('status', Admission::ACTIVE)
            ->where('id', '>', $admission->id)
            ->orderBy('id')
            ->limit(4)
            ->get();

        return view('nqadmin-admission::frontend.school', compact(
            'admission',
            'topPosts',
            'relatedSchool'
        ));
    }

    private function getTopPostsOfAdmission(Admission $admission)
    {
        $types = array_column(AdmissionPost::postTypes(), 'value');
        // Tạo placeholders: “?,?,?,...” cho IN(...)
        $inBinding = implode(',', array_fill(0, count($types), '?'));
        $sql = "
        SELECT
            posts.id,
            posts.admission_id,
            posts.type,
            posts.name,
            posts.slug,
            posts.`index`,
            posts.year,
            posts.status,
            posts.rn,
            counts.total
        FROM (
            -- đánh số mỗi bản theo type
            SELECT
            admission_post.*,
            @rn := IF(@prev = admission_post.type, @rn + 1, 1) AS rn,
            @prev := admission_post.type
            FROM (
            SELECT
                id,
                admission_id,
                type,
                name,
                slug,
                `index`,
                year,
                status
            FROM vjc_admission_post
            WHERE admission_id = ?
                AND type IN ({$inBinding})
            ORDER BY type ASC, year DESC, id DESC
            ) AS admission_post
            CROSS JOIN (SELECT @rn := 0, @prev := NULL) AS vars
        ) AS posts
        -- sub-query đếm tổng per type
        JOIN (
            SELECT
            type,
            COUNT(*) AS total
            FROM vjc_admission_post
            WHERE admission_id = ?
            AND type IN ({$inBinding})
            GROUP BY type
        ) AS counts
            ON posts.type = counts.type

        WHERE posts.rn <= 4
        ORDER BY FIELD(posts.type, {$inBinding}), posts.year DESC, posts.id DESC
        ";
        $bindings = array_merge(
            [$admission->id], $types,
            [$admission->id], $types,
            $types
        );
        $postRows = DB::select($sql, $bindings);

        return collect($postRows)->groupBy('type');
    }

    public function getAdmissionPostOld($id, $slug)
    {
        return redirect()->route('front.admission.post', ['id' => $id, 'slug' => $slug]);
    }

    public function getAdmissionPost($id, $slug)
    {
        if (request('p')) {
            $postId = (int) request('p');
            $postYear = AdmissionPost::find($postId);

            if ($postYear) {
                return redirect()->route('front.admission.post', ['id' => $postYear->id, 'slug' => $postYear->slug]);
            }
        }

        $admissionPost = AdmissionPost::find($id);

        if (!$admissionPost) {
            return redirect()->route('front.admission.index')->with('error', 'Tin tức đang cập nhật');
        }

        if ($slug != $admissionPost->slug) {
            return redirect()->route('front.admission.post', ['id' => $admissionPost->id, 'slug' => $admissionPost->slug]);
        }

        $admissionPost->load('admission:id,name,slug,thumbnail,banner,code,admission_area_id,admission_level_id');
        $admissionPost->timestamps = false;
        $admissionPost->update([
            'view' => $admissionPost->view + 1
        ]);

        $admission = $admissionPost->admission;
        $relateds = collect([]);
        $topPosts = collect([]);

        if ($admissionPost->type == AdmissionPost::TYPE_EXAM) {
            $relateds = AdmissionPost::select('id', 'name', 'slug', 'status', 'thumbnail')
                ->where('type', AdmissionPost::TYPE_EXAM)
                ->where('id', '<>', $admissionPost->id)
                ->orderByDesc('id')
                ->limit(4)
                ->get();
        } elseif ($admission) {
            $admission->load('admissionLevel', 'admissionArea');
            $topPosts = $this->getTopPostsOfAdmission($admission);
            $relateds = Admission::select([
                'id',
                'name',
                'slug',
                'status',
                'code',
                'thumbnail',
                'banner',
                'admission_area_id',
                'admission_level_id',
            ])
            ->where('status', Admission::ACTIVE)
            ->where('id', '>', $admission->id)
            ->orderBy('id')
            ->limit(4)
            ->get();
        }

        $yearPosts = collect([]);

        if ($admissionPost->year && $admission) {
            $yearPosts = AdmissionPost::select('id', 'name', 'slug', 'admission_id', 'year')
                ->where('admission_id', $admissionPost->admission_id)
                ->whereNotNull('year')
                ->where('type', $admissionPost->type)
                ->orderByDesc('year')
                ->get();
        }

        return view('nqadmin-admission::frontend.post', compact(
            'admissionPost',
            'admission',
            'topPosts',
            'yearPosts',
            'relateds'
        ));
    }

    public function examIndex()
    {
        $topPosts = $this->getPosts(AdmissionPost::TYPE_EXAM, 3, true);
        $admissionPosts = $this->getPosts(AdmissionPost::TYPE_EXAM, 0);
        $hotPosts = $this->hotPostsWithCache(AdmissionPost::TYPE_EXAM);

        return view('nqadmin-admission::frontend.exam-index', compact(
            'topPosts',
            'admissionPosts',
            'hotPosts'
        ));
    }

    public function benchMarkIndex()
    {
        $topPosts = $this->getPosts(AdmissionPost::TYPE_BENCHMARK, 3, true);
        $admissionPosts = $this->getPosts(AdmissionPost::TYPE_BENCHMARK, 0);
        $hotPosts = $this->hotPostsWithCache(AdmissionPost::TYPE_BENCHMARK);

        return view('nqadmin-admission::frontend.benchmark-index', compact(
            'topPosts',
            'admissionPosts',
            'hotPosts'
        ));
    }

    public function getPosts($type = null, $limit = 0, $isHot = false)
    {
        $query = AdmissionPost::select(
                'id',
                'name',
                'slug',
                'status',
                'type',
                'description',
                'view',
                'thumbnail',
                'updated_at'
            )
            ->where('status', AdmissionPost::ACTIVE)
            ->when($type, function ($query) use ($type) {
                $query->where('type', $type);
            })
            ->when($isHot, function ($query) {
                $query->where('is_hot', AdmissionPost::IS_HOT);
            })
            ->orderByDesc('id');

        if ($limit > 0) {
            return $query
                ->limit($limit)
                ->get();
        }

        return $query->paginate(15);
    }

    private function hotPostsWithCache($type = null)
    {
        return $this->remember(function() use ($type) {
            return AdmissionPost::select(
                'id',
                'name',
                'slug',
                'status',
                'type',
                'description',
                'view',
                'thumbnail',
                'updated_at'
            )
            ->where('status', AdmissionPost::ACTIVE)
            ->when($type, function ($query) use ($type) {
                $query->where('type', $type);
            })
            ->orderByDesc('view')
            ->limit(8)
            ->get();
        });
    }

    public function downloadDocument($id)
    {
        $admissionPost = AdmissionPost::find($id);

        if ($admissionPost && $admissionPost->document) {
            return app()->make(FileService::class)->download($admissionPost->document, $admissionPost->slug);
        } else {
            return redirect()->back();
        }
    }
}
