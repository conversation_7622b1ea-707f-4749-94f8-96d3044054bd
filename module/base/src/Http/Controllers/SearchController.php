<?php

namespace Base\Http\Controllers;

use App\Http\Controllers\Controller;
use Base\Models\TrackingSearch;
use Base\Services\ElasticSearchService;
use Course\Models\Course;
use Course\Models\CourseCurriculumItems;
use MultipleChoices\Models\Question;
use Admission\Models\Admission;
use Admission\Models\AdmissionPost;
use Illuminate\Support\Facades\DB;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class SearchController extends Controller
{
    protected $elasticSearchService;

    public function __construct(ElasticSearchService $elasticSearchService)
    {
        $this->elasticSearchService = $elasticSearchService;
    }

    public function search(Request $request)
    {
        $type = in_array($request->type, ['all', 'exam', 'question']) ? $request->type : 'all';
        $s_keyword = text_clean($request->q);
        $isSearchExam = preg_match('/kiểm tra|trắc nghiệm|đề thi|bài tập/iu', $s_keyword);
        $queryTerm = escapeElastic($s_keyword);
        $page = intval($request->page) ?: 0;
        $sKeywords = [];
        $sExams = collect([]);
        $sQuestions = collect([]);

        try {
            if ($s_keyword) {
                switch ($type) {
                    case 'exam':
                    case 'question':
                        $indices = [config('elasticsearch.indexName.kh_' . $type)];
                        $size = ($limit = intval($request->limit)) > 0 ? min($limit, 30) : 30;
                        $from = $page * $size;

                        $params = [
                            'index' => $indices,
                            'body' => [
                                'size' => $size,
                                'from' => $from,
                                'query' => [
                                    'bool' => [
                                        'must' => [
                                            [
                                                'multi_match' => [
                                                    'query' => $queryTerm,
                                                    'fields' => ['title', 'name'],
                                                    'operator' => 'or',
                                                ],
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                        ];

                        $response = $this->elasticSearchService->connection()->search($params);
                        $sdata = $response['hits']['hits'] ?? [];

                        if (!empty($sdata)) {
                            if ($type == 'question') {
                                $sQuestions = Question::select('id', 'title', 'slug', 'content', 'curriculum_item', 'view', 'updated_at')
                                    ->whereIn('id', array_column($sdata, '_id'))
                                    ->with([
                                        'getLecture' => function($query) {
                                            $query->select('id', 'name', 'course_id')
                                                ->with(['getCourse' => function($query) {
                                                    $query->select('id', 'slug');
                                                }]);
                                        },
                                        // 'getAnswer' => function($query) {
                                        //     $query->select('id', 'content', 'question');
                                        // },
                                    ])
                                    ->orderBy('updated_at', 'DESC')
                                    ->orderBy('id', 'DESC')
                                    ->get();
                            } else {
                                $sExams = CourseCurriculumItems::select('id', 'name', 'course_id', 'daily_view', 'count2week', 'updated_at')
                                    ->whereIn('id', array_column($sdata, '_id'))
                                    ->withCount('getAllQuestion')
                                    ->with(['getCourse' => function($query) {
                                        $query->select('id', 'slug', 'view');
                                    }])
                                    ->orderBy('daily_view', 'DESC')
                                    ->orderBy('count2week', 'DESC')
                                    ->orderBy('id', 'DESC')
                                    ->get();
                            }
                        }

                        break;
                    case 'all':
                    default:
                        $indices = array_values(config('elasticsearch.indexName'));
                        $size = ($limit = intval($request->limit)) > 0 ? min($limit, 6) : 6;
                        $from = $page * $size;

                        $params = [
                            'index' => $indices,
                            'body' => [
                                'size' => 0, // Không lấy hits chính, chỉ lấy aggregations
                                'query' => [
                                    'bool' => [
                                        'should' => [
                                            [
                                                'bool' => [
                                                    'must' => [
                                                        ['match' => ['title' => $queryTerm]],
                                                        ['term' => ['_index' => config('elasticsearch.indexName.kh_keyword')]]
                                                    ]
                                                ]
                                            ],
                                            [
                                                'bool' => [
                                                    'must' => [
                                                        ['match' => ['name' => $queryTerm]],
                                                        ['term' => ['_index' => config('elasticsearch.indexName.kh_exam')]]
                                                    ]
                                                ]
                                            ],
                                            [
                                                'bool' => [
                                                    'must' => [
                                                        ['match' => ['title' => $queryTerm]],
                                                        ['term' => ['_index' => config('elasticsearch.indexName.kh_question')]]
                                                    ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ],
                                'aggs' => [
                                    'keywords' => [
                                        'filter' => ['term' => ['_index' => config('elasticsearch.indexName.kh_keyword')]],
                                        'aggs' => [
                                            'top_keywords' => [
                                                'top_hits' => [
                                                    'size' => $size,
                                                    'from' => $from,
                                                    // 'highlight' => [
                                                    //     'fields' => [
                                                    //         'name' => new \stdClass()
                                                    //     ]
                                                    // ]
                                                ]
                                            ]
                                        ]
                                    ],
                                    'exams' => [
                                        'filter' => ['term' => ['_index' => config('elasticsearch.indexName.kh_exam')]],
                                        'aggs' => [
                                            'top_exams' => [
                                                'top_hits' => [
                                                    'size' => $size,
                                                    'from' => $from,
                                                    // 'highlight' => [
                                                    //     'fields' => [
                                                    //         'name' => new \stdClass()
                                                    //     ]
                                                    // ]
                                                ]
                                            ]
                                        ]
                                    ],
                                    'questions' => [
                                        'filter' => ['term' => ['_index' => config('elasticsearch.indexName.kh_question')]],
                                        'aggs' => [
                                            'top_questions' => [
                                                'top_hits' => [
                                                    'size' => $size,
                                                    'from' => $from,
                                                    // 'highlight' => [
                                                    //     'fields' => [
                                                    //         'title' => new \stdClass()
                                                    //     ]
                                                    // ]
                                                ]
                                            ]
                                        ]
                                    ]
                                ]
                            ],
                        ];

                        $response = $this->elasticSearchService->connection()->search($params);
                        $sKeywords = $response['aggregations']['keywords']['top_keywords']['hits']['hits'] ?? [];
                        $exams = $response['aggregations']['exams']['top_exams']['hits']['hits'] ?? [];
                        $questions = $response['aggregations']['questions']['top_questions']['hits']['hits'] ?? [];

                        if (!empty($exams)) {
                            $sExams = CourseCurriculumItems::select('id', 'name', 'course_id', 'daily_view', 'count2week', 'updated_at')
                                ->whereIn('id', array_column($exams, '_id'))
                                ->withCount('getAllQuestion')
                                ->with(['getCourse' => function($query) {
                                    $query->select('id', 'slug', 'view');
                                }])
                                ->orderBy('daily_view', 'DESC')
                                ->orderBy('count2week', 'DESC')
                                ->orderBy('id', 'DESC')
                                ->get();
                        }

                        if (!empty($questions)) {
                            $sQuestions = Question::select('id', 'title', 'slug', 'content', 'curriculum_item', 'view', 'updated_at')
                                ->whereIn('id', array_column($questions, '_id'))
                                ->with(['getLecture' => function($query) {
                                    $query->select('id', 'name', 'course_id')
                                        ->with(['getCourse' => function($query) {
                                            $query->select('id', 'slug');
                                        }]);
                                }])
                                ->orderBy('updated_at', 'DESC')
                                ->orderBy('id', 'DESC')
                                ->get();
                        }

                        break;
                }
            } else {
                switch ($type) {
                    case 'exam':
                        $size = 30;
                        $sExams = CourseCurriculumItems::select('id', 'name', 'type', 'course_id', 'daily_view', 'count2week', 'updated_at')
                            ->where('type', 'test')
                            ->withCount('getAllQuestion')
                            ->with(['getCourse' => function($query) {
                                $query->select('id', 'slug', 'view');
                            }])
                            ->orderBy('id')
                            ->limit($size)
                            ->get();

                        break;
                    case 'question':
                        $size = 30;
                        $sQuestions = Question::select('id', 'title', 'slug', 'content', 'curriculum_item', 'view', 'updated_at')
                            ->with([
                                'getLecture' => function($query) {
                                    $query->select('id', 'name', 'course_id')
                                        ->with(['getCourse' => function($query) {
                                            $query->select('id', 'slug');
                                        }]);
                                },
                            ])
                            ->orderBy('id', 'DESC')
                            ->limit($size)
                            ->get();

                        break;
                    case 'all':
                    default:
                        $size = 6;
                        $sExams = CourseCurriculumItems::select('id', 'name', 'type', 'course_id', 'daily_view', 'count2week', 'updated_at')
                            ->where('type', 'test')
                            ->withCount('getAllQuestion')
                            ->with(['getCourse' => function($query) {
                                $query->select('id', 'slug', 'view');
                            }])
                            ->orderBy('id')
                            ->limit($size)
                            ->get();
                        $sQuestions = Question::select('id', 'title', 'slug', 'content', 'curriculum_item', 'view', 'updated_at')
                            ->with([
                                'getLecture' => function($query) {
                                    $query->select('id', 'name', 'course_id')
                                        ->with(['getCourse' => function($query) {
                                            $query->select('id', 'slug');
                                        }]);
                                },
                            ])
                            ->orderBy('id', 'DESC')
                            ->limit($size)
                            ->get();

                        break;
                }
            }
        } catch (\Exception $e) {
            report($e);
        }

        if ($request->ajax()) {
            switch ($type) {
                case 'exam':
                    return response()->json([
                        'hasMore' => $sExams->count() >= $size,
                        'nextPageUrl' => route('search.query', [
                            'q' => $s_keyword,
                            'type' => 'exam',
                            'page' => $page + 1,
                        ]),
                        'html' => view('nqadmin-dashboard::frontend.partials.search-results-exam', [
                            'sExams' => $sExams,
                        ])->render(),
                    ]);

                    break;
                case 'question':
                    return response()->json([
                        'hasMore' => $sQuestions->count() >= $size,
                        'nextPageUrl' => route('search.query', [
                            'q' => $s_keyword,
                            'type' => 'question',
                            'page' => $page + 1,
                        ]),
                        'html' => view('nqadmin-dashboard::frontend.partials.search-results-question', [
                            'sQuestions' => $sQuestions,
                        ])->render(),
                    ]);

                    break;
                case 'all':
                default:
                    return response()->json([
                        'html' => view('nqadmin-dashboard::frontend.partials.search-results-header', [
                            'sKeywords' => $sKeywords,
                            'sExams' => $sExams,
                            'sQuestions' => $sQuestions,
                            's_keyword' => $s_keyword,
                            'isSearchExam' => $isSearchExam,
                        ])->render(),
                    ]);

                    break;
            }
        } else {
            if ($s_keyword) {
                $s_keyword_slug = str_to_slug($s_keyword, 150);

                TrackingSearch::updateOrCreate([
                    'slug' => $s_keyword_slug
                ], [
                    'keyword' => str_limit($s_keyword, 1000),
                    'qty' => DB::raw('qty + 1'),
                ]);
            }

            return view('nqadmin-dashboard::frontend.search-results', [
                'sExams' => $sExams,
                'sQuestions' => $sQuestions,
                's_keyword' => $s_keyword,
                'type' => $type,
                'size' => $size,
                'page' => $page,
                'isSearchExam' => $isSearchExam,
            ]);
        }
    }

    public function admissionSearch(Request $request)
    {
        $s_keyword = text_clean($request->q);
        $queryTerm = strtolower(escapeElastic($s_keyword));
        $page = intval($request->page) ?: 0;
        $size = ($limit = intval($request->limit)) > 0 ? min($limit, 10) : 10;
        $types = array_column(AdmissionPost::postTypes(), 'value');
        $sAdmissions = collect([]);
        $type = null;

        if ($s_keyword) {
            if (in_array($request->type, $types)) {
                $type = $request->type;
            }

            try {
                $indices = [config('elasticsearch.indexName.kh_admission')];
                $from = $page * $size;
                $params = [
                    'index' => $indices,
                    'body'  => [
                        'size'  => $size,
                        'from'  => $from,
                        // 'sort' => [
                        //     [
                        //         'id' => [
                        //             'order'         => 'asc',
                        //             'unmapped_type' => 'long'
                        //         ]
                        //     ]
                        // ],
                        'query' => [
                            'bool' => [
                                'should' => [
                                    [
                                        'wildcard' => [
                                            'code' => [
                                                'value' => "*{$queryTerm}*",
                                                'boost' => 5
                                            ]
                                        ]
                                    ],
                                    [
                                        'match' => [
                                            'name' => [
                                                'query' => $queryTerm,
                                                'fuzziness' => 'AUTO',
                                                'boost' => 1
                                            ]
                                        ]
                                    ]
                                ]
                            ]
                        ]
                    ]
                ];

                $response = $this->elasticSearchService->connection()->search($params);
                $hitData = $response['hits']['hits'] ?? [];

                if (!empty($hitData)) {
                    $sAdmissions = collect($hitData)
                        ->pluck('_source');
                }
            } catch (\Exception $e) {
                report($e);
            }
        } else {
            $sAdmissions = Admission::select('id', 'name', 'slug', 'code')
                ->orderBy('id')
                ->limit($size)
                ->get();
        }

        if ($request->ajax()) {
            return response()->json([
                'html' => view('nqadmin-admission::frontend.partials.search-results', [
                    'sAdmissions' => $sAdmissions->toArray(),
                    'type' => $type,
                    's_keyword' => $s_keyword,
                ])->render(),
            ]);
        } else {
            abort(404);
        }
    }
}
