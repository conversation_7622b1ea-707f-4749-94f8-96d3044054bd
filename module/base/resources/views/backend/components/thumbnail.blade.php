@php
    $id = $name ?? 'thumbnail';
@endphp

<div class="card">
    <div class="card-header">
        <h5 class="card-title">{{ $title ?? "Ảnh đại diện" }}</h5>
    </div>
    <div class="card-body">
        <img id="holder_{{ $id }}"
            style="margin-bottom:10px; width: 100%"
            class="img-fluid"
            @if (isset($imgPath) && $imgPath)
            src="{{ asset($imgPath)}}"
            @else
            src="{{ (!empty(old('thumbnail'))) ? asset(old('thumbnail')) : asset_cdn('adminux/img/no-image.jpg') }}"
            @endif
        >
        <input id="{{ $id }}" class="form-control" type="file" name="{{ $id }}" style="display: none">
        <div class="input-group">
            <a data-input="thumbnail" data-preview="holder" class="lfm btn btn-warning" href="javascript:;"
               onclick="return $('#{{ $id }}').click();">
                <i class="fa fa-picture-o"></i> Chọn ảnh
            </a>
        </div>
    </div>
</div>

@push('js')
    <script>
        $('#{{ $id }}').change(function () {
            var val = $(this).val().toLowerCase(),
                regex = new RegExp("(.*?)\.(jpg|jpeg|gif|png)$");

            if (!(regex.test(val))) {
                $(this).val('');
                alert('Xin hãy chọn đúng định dạng ảnh');
            } else {
                var fr = new FileReader;
                var f = 0;
                fr.onload = function (e) { // file is loaded
                    var img = new Image;

                    img.onload = function () {
                        $('#holder_{{ $id }}').attr('src', e.target.result);
                    };

                    img.src = fr.result; // is the data URL because called with readAsDataURL
                };
                fr.readAsDataURL(this.files[0]);
            }
        });
    </script>
@endpush
