<header id="header" class="tg-header tg-headervtwo">
    @if (Agent::isDesktop())
        <div class="tg-middlecontainer">
            <div class="container">
                <strong class="tg-logo">
                    <a href="/"><img src="/images/logo.png" alt="{{ config('app.name') }}"></a>
                </strong>
                <div class="tg-actionlist">
                    @if (Auth::check())
                    <div class="dropdown tg-themedropdown tg-minicartdropdown">
                        <a href="{{ route('front.users.practice_tests.get') }}" class="tg-btnthemedropdown" style="font-weight: 400;">
                            <i class="far fa-folder-open" style="padding-right: 5px;"></i> Thư viện của bạn
                        </a>
                    </div>
                    <div class="dropdown tg-themedropdown tg-wishlistdropdown">
                        <a id="notifications" class="tg-btnthemedropdown cursor-pointer" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-action="{{ route('front.notification.post', ['numberNoti' => $numberNoti]) }}">
                            @if ($numberNoti > 0)
                                <span class="tg-themebadge" id="notifications-count">{{ $numberNoti }}</span>
                            @endif
                            <i class="far fa-bell"></i>
                        </a>
                        <div class="dropdown-menu tg-themedropdownmenu list-notification" aria-labelledby="notifications">
                            <div class="tg-boxlist text-center">
                                <i class="fa fa-spinner fa-pulse js-loading-icon"></i>
                            </div>
                        </div>
                    </div>
                    @else
                    <div class="dropdown tg-themedropdown">
                        <a style="font-size: 16px;" id="tg-wishlisst" class="tg-btnthemedropdown cursor-pointer" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fa fa-lock p-0 mr-3" style="font-size: 14px;"></i>
                            <span>Đăng nhập</span>
                        </a>
                        <div class="dropdown-menu tg-themedropdownmenu" aria-labelledby="tg-wishlisst">
                            <div class="login-box">
                                <a href="#login-box" class="btn btn-popup btn-login">
                                    <i class="fas fa-sign-in-alt"></i> Đăng nhập
                                </a>
                                <a href="#register-box" class="btn btn-popup btn-register">
                                    <i class="fas fa-user-edit"></i> Đăng ký
                                </a>
                            </div>
                        </div>
                    </div>
                    @endif
                    {{-- <div class="dropdown tg-themedropdown tg-minicartdropdown">
                        <a id="vj-cart" class="tg-btnthemedropdown cursor-pointer" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-action="{{ route('front.cart.mycart') }}">
                            @if (Cart::content()->count() > 0)
                                <span class="tg-themebadge">{{ Cart::content()->count() }}</span>
                            @endif
                            <i class="fas fa-cart-plus"></i>
                        </a>
                        <div class="dropdown-menu tg-themedropdownmenu list-cart" aria-labelledby="vj-cart">
                            <div class="tg-boxlist text-center">
                                <i class="fa fa-spinner fa-pulse js-loading-icon"></i>
                            </div>
                        </div>
                    </div> --}}
                    <div class="box-vip_active">
                        <a class="btn btn-vip_active" href="{{ route('dang-ky-vip') }}">
                            <span>Kích hoạt</span> VIP
                        </a>
                    </div>
                </div>
                <div class="tg-searchbox">
                    <form class="tg-formsearch w-100" action="{{ route('search.query') }}" role="search">
                        <fieldset class="vj-search-box">
                            <input type="text" name="q" class="form-control vj-search_input" placeholder="Tìm kiếm đề thi, câu hỏi..." value="{{ text_clean(request('q')) }}" autocomplete="off" aria-label="Tìm kiếm">
                            <button type="submit" aria-label="Tìm kiếm"><i class="fa fa-search"></i></button>
                            <span class="vj-search-close" title="Đóng kết quả tìm kiếm"><i class="fas fa-times" aria-hidden="true"></i></span>
                        </fieldset>
                        <div class="vj-search_results"></div>
                    </form>
                </div>
            </div>
        </div>
    @endif
    <div id="tg-navigationholder" class="tg-navigationarea">
        <div class="container">
            @if (!Agent::isDesktop())
            <div class="shide vj-search-box" id="shide">
                <div class="search-hide">
                    <form style="margin:0px;" action="{{ route('search.query') }}" role="search">
                        <input type="text" name="q" class="vj-search_input" placeholder="Tìm kiếm đề thi, câu hỏi..." value="{{ text_clean(request('q')) }}" autocomplete="off" aria-label="Tìm kiếm">
                        <span class="remove"><i class="fa fa-times"></i></span>
                    </form>
                </div>
            </div>
            <div class="vj-search_results"></div>
            @endif
            <div class="tg-navigationholder">
                <nav id="tg-nav" class="tg-nav">
                    <div class="navbar-header">
                        @if (!Agent::isDesktop())
                            <a href="/" class="m-logo">
                                <img src="{{asset_cdn('images/icon_vj_sm2.png')}}" alt="{{ config('app.name') }}">
                            </a>
                        @endif
                        <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#tg-navigation" aria-expanded="false">
                            <span class="sr-only">Toggle navigation</span>
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                        </button>
                    </div>
                    <div id="tg-navigation" class="collapse navbar-collapse">
                        <div id="scroll-menu" class="tg-navigation">
                            <div id="lscroll-menu" class="lscroll-menu_btn">
                                <span>❮</span>
                            </div>
                            <div id="rscroll-menu" class="rscroll-menu_btn">
                                <span>❯</span>
                            </div>
                            <ul>
                                <li style="position: relative;">
                                    <a class="menu-item-root" href="/" style="padding: 0 15px;"><i class="fas fa-home"></i></a>
                                    <span class="tg-close-navbar" data-toggle="collapse" data-target="#tg-navigation" aria-expanded="false"><i class="fas fa-times-circle"></i></span>
                                </li>
                                @if (!empty($dataCacheComposer['categoriesData']))
                                    @foreach($dataCacheComposer['categoriesData'] as $categoryData)
                                        @if ($categoryData->status === Category\Models\Category::STATUS_HORIZONTAL_TEXT)
                                            <li class="menu-item-has-children menu-item-has-mega-menu" menu-icon="{{ $categoryData->icon }}">
                                                @if ($categoryData->url)
                                                    <a class="menu-item-root" href="{{ $categoryData->url }}">{{ $categoryData->name }}</a>
                                                @else
                                                    <span class="menu-item-root a-item">{{ $categoryData->name }}</span>
                                                @endif
                                                @if ($categoryData->children->count() > 0)
                                                <div class="mega-menu">
                                                    @include('nqadmin-dashboard::frontend.components.header.menu-horizontal', ['categoryData' => $categoryData, 'showText' => true])
                                                </div>
                                                @endif
                                            </li>
                                        @endif
                                        @if ($categoryData->status === Category\Models\Category::STATUS_TAB)
                                            <li class="menu-item-has-children menu-item-has-mega-menu" menu-icon="{{ $categoryData->icon }}">
                                                @if ($categoryData->url)
                                                    <a class="menu-item-root" href="{{ $categoryData->url }}">{{ $categoryData->name }}</a>
                                                @else
                                                    <span class="menu-item-root a-item">{{ $categoryData->name }}</span>
                                                @endif
                                                @if ($categoryData->children->count() > 0)
                                                <div class="mega-menu">
                                                    <ul class="tg-themetabnav" role="tablist">
                                                        @foreach($categoryData->children as $subCategoryData)
                                                        <li role="presentation" class="{{ $loop->first ? 'active' : '' }}">
                                                            <a href="#tab-menu{{ $subCategoryData->id }}" aria-controls="tab-menu{{ $subCategoryData->id }}" role="tab" data-toggle="tab">{{ $subCategoryData->name }}</a>
                                                        </li>
                                                        @endforeach
                                                    </ul>
                                                    <div class="tab-content tg-themetabcontent">
                                                        @foreach($categoryData->children as $subCategoryData)
                                                        <div role="tabpanel" class="tab-pane {{ $loop->first ? 'active' : '' }}" id="tab-menu{{ $subCategoryData->id }}">
                                                            @include('nqadmin-dashboard::frontend.components.header.menu-horizontal', ['categoryData' => $subCategoryData])
                                                        </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                                @endif
                                            </li>
                                        @endif
                                        @if ($categoryData->status === Category\Models\Category::STATUS_HORIZONTAL)
                                            <li class="menu-item-has-children menu-item-has-mega-menu" menu-icon="{{ $categoryData->icon }}">
                                                @if ($categoryData->url)
                                                    <a class="menu-item-root" href="{{ $categoryData->url }}">{{ $categoryData->name }}</a>
                                                @else
                                                    <span class="menu-item-root a-item">{{ $categoryData->name }}</span>
                                                @endif
                                                @if ($categoryData->children->count() > 0)
                                                <div class="mega-menu">
                                                    @include('nqadmin-dashboard::frontend.components.header.menu-horizontal', ['categoryData' => $categoryData])
                                                </div>
                                                @endif
                                            </li>
                                        @endif
                                        @if ($categoryData->status === Category\Models\Category::STATUS_VERTICAL)
                                            <li class="menu-item-has-children" style="position: static;" menu-icon="{{ $categoryData->icon }}">
                                                @if ($categoryData->url)
                                                    <a class="menu-item-root" href="{{ $categoryData->url }}"><span class="fas fa-list-ul mr-2"></span> {{ $categoryData->name }}</a>
                                                @else
                                                    <span class="menu-item-root a-item"><span class="fas fa-list-ul mr-2"></span> {{ $categoryData->name }}</span>
                                                @endif
                                                @if ($categoryData->children->count() > 0)
                                                <ul class="sub-menu">
                                                    @foreach($categoryData->children as $subCategoryData)
                                                        @include('nqadmin-dashboard::frontend.components.header.menu-vertical', ['categoryData' => $subCategoryData])
                                                    @endforeach
                                                </ul>
                                                @endif
                                            </li>
                                        @endif
                                    @endforeach
                                @endif
                            </ul>
                        </div>
                    </div>
                </nav>
                <div id="nav-custom-mb" class="tg-rnav tg-wishlistandcart nav-custom-mb">
                    @if (!Agent::isDesktop())
                        <div class="tg-themedropdown header_search">
                            <a id="searchIcon" class="search-icon cursor-pointer tg-btnthemedropdown"><i class="fa fa-search"></i></a>
                        </div>
                        {{-- <div class="dropdown tg-themedropdown tg-minicartdropdown">
                            <a id="vj-cart" class="tg-btnthemedropdown cursor-pointer" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-action="{{ route('front.cart.mycart') }}">
                                @if (Cart::content()->count() > 0)
                                    <span class="tg-themebadge">{{ Cart::content()->count() }}</span>
                                @endif
                                <i class="fas fa-cart-plus"></i>
                            </a>
                            <div class="dropdown-menu tg-themedropdownmenu list-cart" aria-labelledby="vj-cart">
                                <div class="tg-boxlist text-center">
                                    <i class="fa fa-spinner fa-pulse js-loading-icon"></i>
                                </div>
                            </div>
                        </div> --}}
                        @if (Auth::check())
                            <div class="dropdown tg-themedropdown tg-minicartdropdown">
                                <a href="{{ route('front.users.practice_tests.get') }}" class="tg-btnthemedropdown">
                                    <i class="far fa-folder-open" style="font-size: 20px;"></i>
                                </a>
                            </div>
                            <div class="tg-themedropdown btn-vip">
                                <div class="box-vip_active">
                                    <a class="btn btn-vip_active" href="{{ route('dang-ky-vip') }}" style="margin: 0; padding: 3px 7px;">
                                        <div style="font-size: 14px">Tạo VIP</div>
                                    </a>
                                </div>
                            </div>
                            {{-- <div class="dropdown tg-themedropdown tg-wishlistdropdown">
                                <a id="notifications cursor-pointer" class="tg-btnthemedropdown" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" data-action="{{ route('front.notification.post', ['numberNoti' => $numberNoti]) }}">
                                    @if ($numberNoti > 0)
                                        <span class="tg-themebadge" id="notifications-count">{{ $numberNoti }}</span>
                                    @endif
                                    <i class="far fa-bell"></i>
                                </a>
                                <div class="dropdown-menu tg-themedropdownmenu list-notification" aria-labelledby="notifications">
                                    <div class="tg-boxlist text-center">
                                        <i class="fa fa-spinner fa-pulse js-loading-icon"></i>
                                    </div>
                                </div>
                            </div> --}}
                        @else
                            <div class="dropdown tg-themedropdown tg-wishlistdropdown">
                                <a id="tg-wishlisst" class="tg-btnthemedropdown cursor-pointer" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                    <i class="fa fa-lock p-0 mr-3" style="font-size: 14px;"></i>
                                    <span class="text-white">Đăng nhập</span>
                                </a>
                                <div class="dropdown-menu tg-themedropdownmenu" aria-labelledby="tg-wishlisst">
                                    <div class="login-box">
                                        <a href="#login-box" class="btn btn-popup btn-login">
                                            <i class="fas fa-sign-in-alt"></i> Đăng nhập
                                        </a>
                                        <a href="#register-box" class="btn btn-popup btn-register">
                                            <i class="fas fa-user-edit"></i> Đăng ký
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endif
                    @endif
                    @if (Auth::check())
                        <div class="dropdown tg-themedropdown tg-currencydropdown tg-userlogin">
                            <a id="tg-currenty" class="tg-btnthemedropdown uservip cursor-pointer" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                @if (Auth::user()->orderPackage)
                                    @php
                                        $packageBought = config('web.packages.' . Auth::user()->orderPackage->package) ?? null;
                                    @endphp
                                    <span class="vip-tag">{{ $packageBought['name'] ?? Auth::user()->orderPackage->package }}</span>
                                @endif
                                <div class="img-user">
                                    @include('nqadmin-users::frontend.components.user.thumbnail', ['user' => Auth::user()])
                                </div>
                            </a>
                            <ul class="dropdown-menu tg-themedropdownmenu" aria-labelledby="tg-currenty">
                                <li>
                                    @include('nqadmin-dashboard::frontend.components.header.dropdown.user.info')
                                </li>
                                @if (Auth::user()->orderPackage)
                                <li>
                                    <a href="{{ route('dang-ky-vip') }}">
                                        <i class="fas fa-gem"></i>
                                        <p class="mb-0" style="color: #009688;">
                                            Tài khoản <strong class="text-danger">{{ $packageBought['name'] ?? Auth::user()->orderPackage->package }}</strong>
                                            @if (Auth::user()->orderPackage->expired_at)
                                            hết hạn vào ngày {{ dateFormat(Auth::user()->orderPackage->expired_at, 'd/m/Y') }}
                                            @endif
                                        </p>
                                    </a>
                                </li>
                                @endif
                                @if (Auth::user()->can('access_dashboard'))
                                    <li>
                                        <a href="{{ route('nqadmin::dashboard.index.get') }}">
                                            <i class="fas fa-tachometer-alt"></i>
                                            <p class="mb-0">Truy cập trang quản trị</p>
                                        </a>
                                    </li>
                                @endif
                                @if (Auth::user()->isTeacher())
                                    <li class="history clearfix">
                                        <a href="{{ route('front.users.teacher.get',['code' => Auth::user()->code]) }}">
                                            <i class="far fa-id-card"></i>
                                            <p class="mb-0">Trang cá nhân giáo viên</p>
                                        </a>
                                    </li>
                                @endif
                                <li>
                                    <a href="{{ route('front.users.my_course.get') }}">
                                        <i class="fas fa-shopping-cart"></i>
                                        <p class="mb-0">Khóa học của tôi</p>
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ route('front.users.info.get') }}">
                                        <i class="fas fa-user"></i>
                                        <p class="mb-0">Thông tin cá nhân</p>
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ route('front.users.history.get') }}">
                                        <i class="fas fa-history"></i>
                                        <p class="mb-0">Lịch sử mua hàng</p>
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ route('front.users.practice_tests.get') }}">
                                        <i class="fas fa-tasks"></i>
                                        <p class="mb-0">Trắc nghiệm đã tham gia</p>
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ route('front.users.change_password.get') }}">
                                        <i class="fas fa-key"></i>
                                        <p class="mb-0">Thay đổi mật khẩu</p>
                                    </a>
                                </li>
                                <li>
                                    <a href="{{ route('front.logout.get') }}">
                                        <i class="fas fa-sign-out-alt"></i>
                                        <p class="mb-0">Đăng xuất</p>
                                    </a>
                                </li>
                                @php
                                $otherDevice = Auth::user()->devices()->count();
                                @endphp
                                @if ($otherDevice > 1)
                                <li>
                                    <a href="#logout-other-devices" class="btn-popup">
                                        <i class="far fa-share-square"></i>
                                        <p class="mb-0">Đăng xuất khỏi các thiết bị khác ({{ $otherDevice }})</p>
                                    </a>
                                </li>
                                @endif
                            </ul>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</header>
@if (empty(Auth::user()->orderPackage))
<div class="banner-text">
    <a href="javascript:;" class="banner-text-close">&#x2715;</a>
    <div class="container p-sm-0">
        <div class="banner-text-content">
            <a href="{{ route('dang-ky-vip') }}">
                <p class="banner-text-des mb-0">✨ Đăng kí <strong class="fs-16">VIP</strong> để truy cập không giới hạn. <span class="banner-text-link">Đăng ký ngay</span></p>
            </a>
        </div>
    </div>
</div>
@endif
