<div class="slist">
    @if (!empty($sKeywords))
    <ul class="slistbox">
        @foreach ($sKeywords as $sKeyword)
        <li>
            <a href="{{ route('search.query', ['q' => $sKeyword['_source']['title'], 'type' => 'all']) }}">
                <i class="fa fa-search"></i>
                <span>{{ $sKeyword['_source']['title'] }}</span>
            </a>
        </li>
        @endforeach
    </ul>
    @endif
    @if ($isSearchExam)
        @if (!$sExams->isEmpty())
            @include('nqadmin-dashboard::frontend.partials.search-results-header-exam')
        @endif
        @if (!$sQuestions->isEmpty())
            @include('nqadmin-dashboard::frontend.partials.search-results-header-question')
        @endif
    @else
        @if (!$sQuestions->isEmpty())
            @include('nqadmin-dashboard::frontend.partials.search-results-header-question')
        @endif
        @if (!$sExams->isEmpty())
            @include('nqadmin-dashboard::frontend.partials.search-results-header-exam')
        @endif
    @endif
</div>
<hr>
<p class="text-center m-0 smore">
    <a href="{{ route('search.query', ['q' => $s_keyword, 'type' => 'all']) }}">Xem tất cả các kết quả: <strong>{{ $s_keyword }}</strong></a>
</p>
