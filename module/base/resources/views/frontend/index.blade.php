@extends('nqadmin-dashboard::frontend.master')

@section('content')
    @if (!Agent::isDesktop())
    <div class="hsearch-area pb-0">
        <div class="container">
            <form class="tg-formsearch w-100" action="{{ route('search.query') }}" role="search">
                <fieldset class="vj-search-box">
                    <input style="border-radius: 5px;" type="text" name="q" class="form-control vj-search_input" placeholder="Tìm kiếm đề thi, câu hỏi..." value="" autocomplete="off" aria-label="Tìm kiếm">
                    <button type="submit" style="border-radius: 5px;" aria-label="Tìm kiếm"><i class="fa fa-search"></i></button>
                    <span class="vj-search-close" title="Đóng kết quả tìm kiếm"><i class="fas fa-times" aria-hidden="true"></i></span>
                </fieldset>
                <div class="vj-search_results"></div>
            </form>
        </div>
    </div>
    @endif
    @include('nqadmin-dashboard::frontend.partials.pvip-section')
    @if ($latestTrackingView && $latestTrackingView->count() > 0)
    <div class="hhistory-area">
        <div class="container">
            <div class="nav-history">
                <div class="nav-history_item active">
                    <p class="nav-history_link">
                        Gần đây
                    </p>
                </div>
                <div class="nav-history_item">
                    <a href="{{ route('front.users.practice_tests.get') }}">Lịch sử »</a>
                </div>
            </div>
        </div>
    </div>
    <div class="list-history">
        <div class="container">
            <div class="row row-flex">
                @foreach ($latestTrackingView as $trackingView)
                    @if ($trackingView->type == Base\Models\TrackingView::QUESTION_TYPE && $trackingView->question)
                    <div class="col-xs-12 col-md-6">
                        <a href="{{ route('front.question.preview.get', [
                            $trackingView->question->id,
                            $trackingView->question->slug,
                        ]) }}" class="w-100">
                            <div class="l-item">
                                <span class="l-icon" style="color: #795548; background: #fff1ed;">
                                    <i class="fas fa-question-circle"></i>
                                </span>
                                <div class="l-content">
                                    <p class="l-title">{!! $trackingView->question->title ?: strip_tags($trackingView->question->content) !!}</p>
                                    <div class="l-meta">
                                        <span style="font-weight: 500;">• Câu hỏi</span>
                                        <span><i class="far fa-user"></i> {{ number_format_short($trackingView->question->view) }} lượt xem</span>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    @elseif ($trackingView->type == Base\Models\TrackingView::EXAM_TYPE && $trackingView->exam)
                    <div class="col-xs-12 col-md-6">
                        <a href="{{ route('front.exam.get', [
                            $trackingView->exam->getCourse->slug,
                            $trackingView->exam->id,
                        ]) }}" class="w-100">
                            <div class="l-item">
                                <span class="l-icon">
                                    <i class="fas fa-calendar-check"></i>
                                </span>
                                <div class="l-content">
                                    <p class="l-title">{{ $trackingView->exam->name }}</p>
                                    <div class="l-meta">
                                        <span style="font-weight: 500;">• Đề thi</span>
                                        <span><i class="far fa-question-circle"></i> {{  $trackingView->exam->get_all_question_count ?: 'x' }} câu hỏi</span>
                                        <span><i class="far fa-clock"></i> {{ number_format_short($trackingView->exam->getCourse->view + $trackingView->exam->count2week) }} lượt thi</span>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    @endif
                @endforeach
            </div>
        </div>
    </div>
    @endif
    <div class="box-home">
        {{-- @if (!empty($dataHomes['examClasses']))
        <div class="box-home home-cat">
            @if (Agent::isDesktop())
                @include('nqadmin-dashboard::frontend.components.hometop.examclasses-pc')
            @else
                @include('nqadmin-dashboard::frontend.components.hometop.examclasses-mb')
            @endif
        </div>
        @endif --}}
        <div class="container">
            @if (!empty($dataHomes['classes']))
            <div class="row gbox-wrapper mb-30">
                @foreach($dataHomes['classes'] as $classItem)
                    <div class="col-md-4 col-sm-6 col-xs-12 gbox">
                        <div class="card" style="border: 1px solid {{ $classItem->color ?: '#52b700' }};">
                            <div class="card-header">
                                @if ($classItem->books_count > 0)
                                <a data-action="{{ route('front.home.search-choice.class', ['slug' => $classItem->slug]) }}"
                                    style="cursor: pointer;"
                                    class="js-pick-book-btn"
                                    data-toggle="modal"
                                    data-target="#modal-book-picker"
                                    data-classlevel="{{ $classItem->id }}"
                                >
                                @else
                                <a href="{{ route('front.home.search-choice.class', ['slug' => $classItem->slug]) }}">
                                @endif
                                    <div class="gbox-icon" style="background: {{ $classItem->color ?: '#52b700' }};">{{ $classItem->icon }}</div>
                                    <h2 class="gbox-title" style="color: {{ $classItem->color ?: '#52b700' }};">{{ $classItem->name }}</h2>
                                </a>
                            </div>
                            <div class="card-body">
                                @if ($classItem->seo_description)
                                    <p class="gbox-des mb-5">
                                        {{ $classItem->seo_description }}
                                    </p>
                                @endif
                                <p>
                                    <span class="small gbox-star">
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                        <i class="fa fa-star"></i>
                                    </span>
                                </p>
                                @if (!empty($dataHomes['subjects']))
                                    @php
                                        $subjectItems = $dataHomes['subjects']->where('classlevel_id', $classItem->id)->sortByDesc('exam_count');
                                        $totalExamCount = $subjectItems->sum('exam_count');
                                        $totalLectureCount = $subjectItems->sum('lecture_count');
                                    @endphp
                                    <ul>
                                        @foreach($subjectItems->take(3) as $subjectItem)
                                        <li>
                                            <div class="gbox-meta">
                                                <h3 class="mt-0 mb-10 fs-14 fontR">
                                                    @if ($subjectItem->icon)
                                                        <i class="{{ $subjectItem->icon }}"></i>
                                                    @else
                                                        <i class="far fa-folder"></i>
                                                    @endif
                                                    {{ $subjectItem->name }}
                                                </h3>
                                                @if ($subjectItem->books_count > 0)
                                                <a data-action="{{ route('home.exam.class-subject', [
                                                        'classSlug' => $classItem->slug,
                                                        'subjectSlug' => $subjectItem->slug,
                                                    ]) }}"
                                                    class="js-pick-book-btn fs-14 cursor-pointer"
                                                    data-toggle="modal"
                                                    data-target="#modal-book-picker"
                                                    data-classlevel="{{ $classItem->id }}"
                                                    data-subject="{{ $subjectItem->id }}"
                                                >
                                                @else
                                                <a href="{{ route('home.exam.class-subject', [
                                                        'classSlug' => $classItem->slug,
                                                        'subjectSlug' => $subjectItem->slug,
                                                        'type' => 'test'
                                                    ]) }}"
                                                    class="fs-14"
                                                >
                                                @endif
                                                    @if ($subjectItem->lecture_count)
                                                    {{ number_format($subjectItem->lecture_count, 0, ',', '.') }} bài giảng -
                                                    @endif
                                                    {{ number_format($subjectItem->exam_count, 0, ',', '.') }} đề thi »
                                                </a>
                                            </div>
                                        </li>
                                        @endforeach
                                    </ul>
                                @endif
                            </div>
                            <div class="card-footer">
                                @if ($classItem->books_count > 0)
                                <a data-action="{{ route('front.home.search-choice.class', ['slug' => $classItem->slug]) }}"
                                    class="js-pick-book-btn fs-14 card-footer-meta cursor-pointer"
                                    data-toggle="modal"
                                    data-target="#modal-book-picker"
                                    data-classlevel="{{ $classItem->id }}"
                                >
                                @else
                                <a href="{{ route('front.home.search-choice.class', ['slug' => $classItem->slug]) }}" class="fs-14 card-footer-meta">
                                @endif
                                    <span>Xem toàn bộ »</span>
                                    @if ($totalLectureCount)
                                        <span class="fs-15 text-muted">
                                            <i class="fas fa-play-circle mr-1" style="color: #ff8383;"></i>
                                            {{ number_format($totalLectureCount, 0, ',', '.') }} bài giảng
                                        </span>
                                    @endif
                                    <span class="fs-15 text-muted">
                                        <i class="far fa-calendar-check mr-1" style="color: #e91e63;"></i>
                                        {{ number_format($totalExamCount ?? 2048, 0, ',', '.') }} bộ đề 
                                    </span>
                                </a>
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
            @endif
        </div>
    </div>
    <div class="contact-area">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <a href="{{ config('app.zalo') }}" class="contact-item" target="_blank">
                        <div class="text-center">
                            <img src="{{ asset_cdn('images/icons/icon-zalo.jpg') }}" alt="Zalo" style="width: 50px;">
                        </div>
                        <h4 class="text-center" style="color: #0084da;">Zalo VietJack Official</h4>
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="{{ config('app.messenger') }}" class="contact-item" target="_blank">
                        <div class="text-center">
                            <img src="{{ asset_cdn('images/icons/icon-messenger.png') }}" alt="Facebook" style="width: 50px;">
                        </div>
                        <h4 class="text-center" style="color: #0084da;">Facebook</h4>
                    </a>
                </div>
                <div class="col-md-4">
                    <a href="tel:{{ config('app.hotline') }}" class="contact-item">
                        <div class="text-center">
                            <img src="{{ asset_cdn('images/icons/icon-phone.png') }}" alt="Hotline" style="width: 50px;">
                        </div>
                        <h4 class="text-center">{{ config('app.hotline') }}</h4>
                    </a>
                </div>
            </div>
        </div>
    </div>
    @if (!empty($dataHomes['topCourses']))
        <div class="container">
            <div class="box-course mb-30">
                <div class="box-title">
                    <h2 class="txt-title pull-left">Khóa học bán chạy</h2>
                    <a class="view-more pull-right" href="{{ route('front.home.search-course') }}">Xem thêm »</a>
                </div>
                @php
                    $chunkTopCourses = $dataHomes['topCourses']->chunk(5);
                @endphp
                @foreach($chunkTopCourses as $topCourses)
                <div class="js-home-course-slide owl-carousel mb-20">
                    @foreach($topCourses as $course)
                        @include('nqadmin-course::frontend.components.course.course-item')
                    @endforeach
                </div>
                @endforeach
            </div>
        </div>
    @endif
@endsection
