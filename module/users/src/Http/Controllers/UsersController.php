<?php

namespace Users\Http\Controllers;

use Acl\Repositories\RoleRepository;
use Barryvdh\Debugbar\Controllers\BaseController;
use Base\Services\Image\ImageService;
use Base\Supports\FlashMessage;
use Cart\Repositories\OrderDetailsRepository;
use Cart\Repositories\OrdersRepository;
use Course\Repositories\CurriculumProgressRepository;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Media\Repositories\MediaRepository;
use Users\Http\Requests\UserCreateRequest;
use Users\Http\Requests\UserEditRequest;
use Users\Http\Requests\PasswordEditRequest;
use Illuminate\Http\Request;
use Users\Models\Users;
use Users\Models\UsersMeta;
use Acl\Models\Role;
use ClassLevel\Models\ClassLevel;
use Subject\Models\Subject;
use Cart\Models\OrderPackage;
use Users\Models\Device;
use Course\Models\Course;
use Cart\Models\Order;
use Users\Repositories\UsersRepository;
use Carbon\Carbon;
use Base\Traits\HashCode;
use Illuminate\Support\Str;

class UsersController extends BaseController
{
    use HashCode;

    protected $usersRepository;
    protected $mediaRepository;
    protected $imageService;

    public function __construct(
        UsersRepository $usersRepository,
        MediaRepository $mediaRepository,
        ImageService $imageService
    )
    {
        $this->usersRepository = $usersRepository;
        $this->mediaRepository = $mediaRepository;
        $this->imageService = $imageService;
    }

    public function getSetting()
    {
        return view('nqadmin-users::backend.components.setting');
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getIndex(Request $request)
    {
        if (!empty($request->email)) {
            $users = Users::where('email',$request->email)->orWhere('id', $request->email);
        } else {
            $users = New Users();
        }

        if (!empty($request->first_name)) {
            $users = $users->where('first_name', 'like', '%' . $request->first_name . '%');
        }

        if (!empty($request->phone)) {
            $users = $users->where('phone', 'like', '%' . $request->phone . '%');
        }

        if (!empty($request->sex)) {
            $users = $users->where('sex', 'like', '%' . $request->sex . '%');
        }

        if (!empty($request->role)) {
            $users = $users->join('role_user', function ($join) use ($request) {
                $join->on('users.id', '=', 'role_user.user_id')
                    ->where('role_user.role_id', $request->role);
            });
        }

        $users = $users->with('roles', 'devices')->orderBy('id', 'desc')->paginate(25);

        return view('nqadmin-users::backend.components.index', [
            'data' => $users,
            'list_roles' => Role::all(),
        ]);
    }

    /**
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getCreate(RoleRepository $roleRepository)
    {
        $role = $roleRepository->all();

        return view('nqadmin-users::backend.components.create', [
            'role' => $role
        ]);
    }

    /**
     * @param \Users\Http\Requests\UserCreateRequest $request
     *
     * @return bool|\Illuminate\Http\RedirectResponse
     */
    public function postCreate(UserCreateRequest $request)
    {
        try {
            $data = $request->except(['_token', 'continue_edit', 'thumbnail']);

            $user = $this->usersRepository->create([
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'first_name' => $request->first_name,
                'phone' => $request->phone,
                'sex' => $request->sex,
                'position' => $request->position,
                'status' => $request->status,
            ]);

            if (!empty($request->thumbnail)) {
                $avarta = $this->mediaRepository->upload($user->id, $request->thumbnail, null, 'avarta');
                $user->thumbnail = $avarta->url;
                $user->save();
            }

            $user->genCode();

            if (!empty($request->role)) {
                $user->roles()->sync($request->role);
            }

            if ($request->has('continue_edit')) {
                return redirect()->route('nqadmin::users.edit.get', [
                    'id' => $user->id
                ])->with(FlashMessage::returnMessage('create'));
            }

            return redirect()->route('nqadmin::users.index.get')->with(FlashMessage::returnMessage('create'));
        } catch (\Exception $e) {
            return redirect()->back()->withErrors($e->getMessage());
        }
    }

    public function getEdit($id, RoleRepository $roleRepository)
    {
        $user = $this->usersRepository->find($id);
        $roles = $roleRepository->all();
        $subjects = Subject::all();
        $classes = ClassLevel::all();

        return view('nqadmin-users::backend.components.edit', [
            'data' => $user,
            'role' => $roles,
            'subjects' => $subjects,
            'classes' => $classes,
        ]);
    }

    /**
     * @param \Users\Http\Requests\UserEditRequest $request
     */
    public function postEdit($id, UserEditRequest $request)
    {
        try {
            DB::beginTransaction();

            $users = $this->usersRepository->update([
                // 'email' => $request->email,
                'first_name' => $request->first_name,
                'phone' => $request->phone,
                'sex' => $request->sex,
                'position' => $request->position,
                'status' => $request->status,
            ], $id);

            if (!empty($request->thumbnail)) {
                $avarta = $this->mediaRepository->upload($users->id, $request->thumbnail, null, 'avarta', $users->thumbnail);
                $users->thumbnail = $avarta->url;
                $users->save();
            }

            if (!empty($request->role)) {
                $users->roles()->sync($request->role);
            } else {
                $users->roles()->sync([]);
            }

            if (!empty($request->class_id)) {
                UsersMeta::updateOrCreate(
                    ['users_id' => $users->id, 'meta_key' => 'class_id'],
                    [
                        'meta_value' => $request->class_id,
                    ]
                );
            }

            if (!empty($request->subject_id)) {
                UsersMeta::updateOrCreate(
                    ['users_id' => $users->id, 'meta_key' => 'subject_id'],
                    [
                        'meta_value' => $request->subject_id,
                    ]
                );
            }

            DB::commit();

            return redirect()->back()->with(FlashMessage::returnMessage('edit'));
        } catch (\Exception $e) {
            DB::rollBack();
            report($e);

            return redirect()->back()->withErrors(config('messages.error'));
        }
    }


    /**
     * @param $id
     *
     * @return \Illuminate\Contracts\View\Factory|\Illuminate\View\View
     */
    public function getEditPassword($id, RoleRepository $roleRepository)
    {
        $user = $this->usersRepository->find($id);

        return view('nqadmin-users::backend.components.edit-password', [
            'data' => $user,
        ]);
    }

    public function postEditPassword($id, PasswordEditRequest $request)
    {
        $password = $request->get('password');

        $user = $this->usersRepository->update([
            'password' => Hash::make($password),
        ], $id);

        return redirect()->back()->with(FlashMessage::returnMessage('edit'));
    }

    /**
     * @param $id
     *
     * @return $this|\Illuminate\Http\RedirectResponse
     */
    public function getDelete(
        $id,
        OrderDetailsRepository $orderDetailsRepository,
        OrdersRepository $ordersRepository,
        CurriculumProgressRepository $curriculumProgressRepository
    )
    {
        if (Course::where('author', $id)->exists()) {
            return redirect()->back()->withErrors('Không thể xóa user vì còn liên kết khóa học.');
        };
        $orderDetailsRepository->deleteWhere(['customer' => $id]);
        $ordersRepository->deleteWhere(['customer' => $id]);
        $curriculumProgressRepository->deleteWhere(['student' => $id]);

        return getDelete($id, $this->usersRepository);
    }

    public function deleteDevice($id)
    {
        Device::where('id', $id)->delete();

        return redirect()->back()->with(FlashMessage::returnMessage('delete'));
    }

    public function changeStatus(Request $request)
    {
        $inputs = $request->all();

        try {
            $data = ['status' => $inputs['status'] == 'true' ? 'active' : 'disable'];
            $users = $this->usersRepository->update($data, $inputs['user_id']);

            return response()->json(['status' => 200]);
        } catch (Exception $e) {
            return response()->json(['status' => 500]);
        }
    }

    public function vipIndex(Request $request)
    {
        $subjects = Subject::all();
        $classes = ClassLevel::all();
        $utmSources = Order::whereNotNull('utm_source')
            ->select('utm_source')
            ->distinct()
            ->pluck('utm_source')
            ->toArray();
        $orderPackages = OrderPackage::with([
            'order' => function ($query) {
                $query->with(['getInvoice', 'detail.course']);
            },
            'getCustomer' => function ($query) {
                $query->with([
                    'getTrackingView' => function($query) {
                        $query->with([
                            'exam' => function ($query) {
                                $query->select(
                                    'id',
                                    'name',
                                    'course_id',
                                    'daily_view',
                                    'count2week'
                                )
                                ->with(['getCourse' => function($query) {
                                    $query->select(
                                        'id',
                                        'slug',
                                        'classlevel',
                                        'subject'
                                    )
                                    ->with([
                                        'getClassLevel' => function($query) {
                                            $query->select(
                                                'id',
                                                'slug',
                                                'name'
                                            );
                                        },
                                        'getSubject' => function($query) {
                                            $query->select(
                                                'id',
                                                'slug',
                                                'name'
                                            );
                                        },
                                    ]);
                                }]);
                            },
                        ])
                        ->limit(4);
                    },
                    'data' => function($query) {
                        $query->where('meta_key', 'class_level');
                    },
                ]);
            },
        ]);

        if ($request->email || $request->first_name || $request->phone) {
            $orderPackages->whereHas('getCustomer', function ($query) use ($request) {
                if ($request->first_name) {
                    $query->where('first_name', 'like', '%' . $request->first_name . '%');
                }

                if ($request->phone) {
                    $query->where('phone', $request->phone);
                }

                if ($request->email) {
                    $query->where('email', $request->email);
                }
            });
        }

        if ($request->order_code) {
            $orderPackages->whereHas('order', function ($query) use ($request) {
                $query->where('code', 'like', '%' . $request->order_code . '%');
            });
        }

        if ($request->package !== null) {
            $orderPackages->where('package', $request->package);
        }

        if ($request->status !== null) {
            switch ($request->status) {
                case 1:
                    $orderPackages->where('status', OrderPackage::ACTIVE);
                    break;
                case 2:
                    $now = Carbon::now();
                    $orderPackages->where('expired_at', '<', $now);
                    break;
                case 3:
                    $orderPackages->where('status', OrderPackage::DISABLED);
                    break;
            }
        }

        if ($request->type !== null) {
            switch ($request->type) {
                case 0:
                    $orderPackages->where('type', OrderPackage::TYPE_USED)
                        ->orderBy('id', 'DESC');
                    break;
                case 1:
                    $orderPackages->where('type', OrderPackage::TYPE_NEW)
                        ->orderBy('id', 'ASC');
                    break;
            }
        } else {
            $orderPackages->where('type', OrderPackage::TYPE_USED)
                ->orderBy('id', 'DESC');
        }

        if ($request->utm_source) {
            $orderPackages->whereHas('order', function($query) use ($request) {
                $query->where('utm_source', $request->utm_source);
            });
        }

        $orderPackages = $orderPackages->paginate(25);

        return view('nqadmin-users::backend.components.vip', [
            'orderPackages' => $orderPackages,
            'subjects' => $subjects,
            'classes' => $classes,
            'utmSources' => $utmSources,
        ]);
    }

    public function vipCreate(Request $request)
    {
        $user = null;

        if ($request->user_id) {
            $user = Users::find($request->user_id);
        }

        return view('nqadmin-users::backend.components.edit-vip', [
            'orderPackage' => new OrderPackage,
            'action' => route('nqadmin::users.vip.store'),
            'user' => $user,
        ]);
    }

    public function vipStore(Request $request)
    {
        try {
            DB::beginTransaction();

            $package = config('web.packages.' . $request->package) ?? null;

            if (!$package) {
                return back()->withErrors('Dịch vụ không tồn tại.');
            }

            $user = Users::find($request->user_id);

            if (!$user) {
                return redirect()->route('nqadmin::users.index.get')->withErrors('Không tìm thấy user!');
            }

            if ($request->phone && $request->phone != $user->phone) {
                $user->update(['phone' => $request->phone]);
            }

            $date_duration = $request->date_duration ?: $package['duration'];

            OrderPackage::create([
                'customer' => $user->id,
                'package' => $request->package,
                'class_levels' => $request->class_levels,
                'subjects' => $request->subjects,
                'status' => $request->status,
                'date_duration' => $date_duration,
                'expired_at' => Carbon::now()->addMonths($date_duration),
            ]);

            DB::commit();

            return redirect()->route('nqadmin::users.vip.get')->with(FlashMessage::returnMessage('create'));
        } catch (\Exception $e) {
            DB::rollBack();
            report($e);

            return redirect()->back()->withErrors(config('messages.error'));
        }
    }

    public function vipEdit(OrderPackage $orderPackage)
    {
        return view('nqadmin-users::backend.components.edit-vip', [
            'orderPackage' => $orderPackage,
            'action' => route('nqadmin::users.vip.update', $orderPackage),
            'user' => $orderPackage->getCustomer,
        ]);
    }

    public function vipUpdate(OrderPackage $orderPackage, Request $request)
    {
        try {
            DB::beginTransaction();

            $package = config('web.packages.' . $request->package) ?? null;

            if (!$package) {
                return redirect()->back()->withErrors('Dịch vụ không tồn tại.');
            }

            if (!$request->user_id) {
                return redirect()->back()->withErrors('Vui lòng chọn tài khoản!');
            }

            $user = Users::find($request->user_id);

            if (!$user) {
                return redirect()->route('nqadmin::users.index.get')->withErrors('Không tìm thấy user!');
            }

            if ($request->phone && $request->phone != $user->phone) {
                $user->update(['phone' => $request->phone]);
            }

            $expired_at = $orderPackage->expired_at;
            $date_duration = $request->date_duration ?: $package['duration'];

            if ($request->status == OrderPackage::ACTIVE) {
                if ($request->status != $orderPackage->status && $expired_at != null) {
                    // lần đầu tiên update đơn hàng thành công thì expired_at tính từ thời điểm active
                    $expired_at = Carbon::now()->addMonths($date_duration);
                } else {
                    // khi đơn hàng đã thành công và muốn gia hạn thì expired_at tính từ thời điểm tạo đơn hàng
                    $expired_at = Carbon::parse($orderPackage->created_at)->addMonths($date_duration);
                }
            }

            $orderPackage->update([
                'customer' => $user->id,
                'package' => $request->package,
                'class_levels' => $request->class_levels,
                'subjects' => $request->subjects,
                'status' => $request->status,
                'date_duration' => $date_duration,
                'expired_at' => $expired_at,
                'type' => OrderPackage::TYPE_USED,
            ]);

            if ($orderPackage->order) {
                $orderPackage->order->update([
                    'status' => $request->status == OrderPackage::ACTIVE ? 'done' : 'create',
                ]);
            }

            DB::commit();

            return redirect()->back()->with(FlashMessage::returnMessage('edit'));
        } catch (\Exception $e) {
            DB::rollBack();
            report($e);

            return redirect()->back()->withErrors(config('messages.error'));
        }
    }

    public function vipDelete(OrderPackage $orderPackage)
    {
        $orderPackage->delete();

        return redirect()->route('nqadmin::users.vip.get')->with(FlashMessage::returnMessage('delete'));
    }

    public function codeVipCreate()
    {
        return view('nqadmin-users::backend.components.create-codevip');
    }

    public function codeVipStore(Request $request)
    {
        $package = config('web.packages.' . $request->package) ?? null;

        if (!$package) {
            return back()->withErrors('Dịch vụ không tồn tại.');
        }

        $maxId = OrderPackage::max('id') ?: 1;
        $quantity = intval($request->quantity) ?: 1;
        $orderPackages = [];
        $num = $maxId * 10 + 1; // đảm bảo chẳng may trong lúc create có bản ghi được tạo ở chỗ khác khiến maxId thay đổi

        $batchSize = 500; // Chia thành các lô nhỏ
        $maxRetries = 3;

        for ($i = 0; $i < $quantity; $i++) {
            $incrementNum = $num + $i;
            $encoded = $this->encodeHashCode($incrementNum);
            $date_duration = $request->date_duration ?: $package['duration'];

            $orderPackages[] = [
                'customer' => 1, // mặc định gán cho admin có id là 1
                'package' => $request->package,
                'code' => $encoded,
                'type' => OrderPackage::TYPE_NEW,
                'status' => OrderPackage::DISABLED,
                'date_duration' => $date_duration,
                'expired_at' => Carbon::now()->addMonths($date_duration),
                'created_at' => now(),
                'updated_at' => now(),
            ];

            if (count($orderPackages) == $batchSize || ($i + 1) == $quantity) {
                $attempt = 0;
                $inserted = false;

                while (!$inserted && $attempt < $maxRetries) {
                    try {
                        DB::transaction(function () use (&$orderPackages) {
                            OrderPackage::insert($orderPackages);
                        });
                        $inserted = true;
                    } catch (\Illuminate\Database\QueryException $e) {
                        // Kiểm tra lỗi trùng lặp
                        if ($e->getCode() == '23000') { // Lỗi vi phạm ràng buộc unique
                            // Retry bằng cách tạo lại các bản ghi bị trùng
                            foreach ($orderPackages as &$record) {
                                $record['code'] = Str::upper(Str::random(8));
                            }
                            $attempt++;
                        } else {
                            // Ném lại ngoại lệ nếu lỗi khác
                            throw $e;
                        }
                    }
                }

                if (!$inserted) {
                    return back()->withErrors('Không thể tạo bản ghi do code trùng lặp mã sau ' . $maxRetries . ' lần thử.');
                }

                // Reset mảng để chuẩn bị cho batch tiếp theo
                $orderPackages = [];
            }
        }

        return redirect()->route('nqadmin::users.vip.get', ['type' => OrderPackage::TYPE_NEW])->with(FlashMessage::returnMessage('create'));
    }

    public function getStatistic(Request $request)
    {
        if (!empty($request->time)) {
            $time = explode(' - ', $request->time);
            $from = reFormatDate($time[0]) . ' 00:00:00';
            $to = reFormatDate($time[1]) . ' 23:59:59';
        } else {
            $from = reFormatDate(date('d/m/Y')) . ' 00:00:00';
            $to = reFormatDate(date('d/m/Y')) . ' 23:59:59';
        }
        $countUsersCreatedAt = Users::whereBetween('created_at', [DATE($from), DATE($to)])->select(DB::raw('count(*) as countAll'), DB::raw('count(phone) as countPhone'))->first();

        $countUsersActive = Users::whereBetween('time_login', [DATE($from), DATE($to)])->count();

        $usersClassLevel12 = $this->getDataFromSingleClass($from, $to, 'lop_12');

        $usersClassLevel11 = $this->getDataFromSingleClass($from, $to, 'lop_11');

        $usersClassLevel10 = $this->getDataFromSingleClass($from, $to, 'lop_10');

        $usersClassLevel9 = $this->getDataFromSingleClass($from, $to, 'lop_9');

        $usersClassLevel8 = $this->getDataFromSingleClass($from, $to, 'lop_8');

        $usersClassLevel7 = $this->getDataFromSingleClass($from, $to, 'lop_7');

        $usersClassLevel6 = $this->getDataFromSingleClass($from, $to, 'lop_6');

        $usersClassLevel5 = $this->getDataFromSingleClass($from, $to, 'lop_5');

        $usersClassLevel4 = $this->getDataFromSingleClass($from, $to, 'lop_4');

        $usersClassLevel3 = $this->getDataFromSingleClass($from, $to, 'lop_3');

        $usersClassLevel2 = $this->getDataFromSingleClass($from, $to, 'lop_2');

        $usersClassLevel1 = $this->getDataFromSingleClass($from, $to, 'lop_1');

        return view('nqadmin-users::backend.components.statistic.users.index')->with([
            'countUsersCreatedAt' => $countUsersCreatedAt,
            'countUsersActive' => $countUsersActive,
            'usersClassLevel12' => $usersClassLevel12,
            'usersClassLevel11' => $usersClassLevel11,
            'usersClassLevel10' => $usersClassLevel10,
            'usersClassLevel9' => $usersClassLevel9,
            'usersClassLevel8' => $usersClassLevel8,
            'usersClassLevel7' => $usersClassLevel7,
            'usersClassLevel6' => $usersClassLevel6,
            'usersClassLevel5' => $usersClassLevel5,
            'usersClassLevel4' => $usersClassLevel4,
            'usersClassLevel3' => $usersClassLevel3,
            'usersClassLevel2' => $usersClassLevel2,
            'usersClassLevel1' => $usersClassLevel1,
        ]);
    }

    private function getDataFromSingleClass($from, $to, $classLevel)
    {
        return Users::whereBetween('created_at', [DATE($from), DATE($to)])
            ->whereHas('data', function ($que) use ($classLevel) {
                return $que->where('meta_key', '=', 'class_level')->where('meta_value', '=', $classLevel);
            })
            ->select(DB::raw('count(*) as countAll'), DB::raw('count(phone) as countPhone'))
            ->first();
    }
}
