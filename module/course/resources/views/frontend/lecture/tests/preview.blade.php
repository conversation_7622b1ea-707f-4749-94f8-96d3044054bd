@extends('nqadmin-dashboard::frontend.master')

@php
    $description = 'Đ<PERSON> thi kiểm tra môn ' . ($course->getSubject ? lcfirst($course->getSubject->name) : '') . ' ' . ($course->getClassLevel ? lcfirst($course->getClassLevel->name) : '') . ' - ' . $lecture->name;
    $totalRatings = $course->getRating->sum('total');
    $sumRatingNumbers = $course->getRating->sum(function($rating) {
        return $rating->rating_number * $rating->total;
    });
    $averageRating = $totalRatings > 0 ? $sumRatingNumbers / $totalRatings : config('rating.avg_default');
    $allQuestionCount = $lecture->getAllQuestion->count();
@endphp

@section('title', $lecture->name)
@section('seo_description', $description)
@section('seo_keywords', 'vietjack, kh<PERSON><PERSON> học, tr<PERSON><PERSON>, đề thi - bài giảng online')
@section('og_title', $lecture->name)
@section('og_description', $description)
@section('og_type', 'article')
@section('snippet')
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "AggregateRating",
        "ratingValue": "{{ number_format($averageRating, 1) }}",
        "ratingCount": "{{ ratingCount($totalRatings, $course->view) }}",
        "itemReviewed": {
            "@type": "CreativeWorkSeries",
            "name": "{{ $course->name }}"
        }
    }
    </script>
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "NewsArticle",
        "headline": "{{ $course->name }}",
        "datePublished": "{{ date(DATE_ISO8601, strtotime($course->updated_at)) }}",
        "dateModified": "{{ date(DATE_ISO8601, strtotime($course->updated_at)) }}",
        "author": [{
            "@type": "Organization",
            "name": "{{ config('app.name') }}",
            "url": "{{ config('app.url') }}"
        }]
    }
    </script>
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [{
            "@type": "ListItem",
            "position": 1,
            "name": "Trang chủ",
            "item": "{{ route('front.home.index.get') }}"
        }, @if($course->getClassLevel){
            "@type": "ListItem",
            "position": 2,
            "name": "{{ $course->getClassLevel->name }}",
            "item": "{{ route('front.home.search-choice.class', [
                'slug' => $course->getClassLevel->slug,
                'type' => $course->type,
                'book' => $course->book_id,
            ]) }}"
        }@endif @if($course->getSubject && $course->getClassLevel), {
            "@type": "ListItem",
            "position": 3,
            "name": "{{ $course->getSubject->name }}",
            "item": "{{ route('home.exam.class-subject', [
                'classSlug' => $course->getClassLevel->slug,
                'subjectSlug' => $course->getSubject->slug,
                'type' => $course->type,
                'book' => $course->book_id,
            ]) }}"
        }@endif
        @if(!$course->getClassLevel) {
            "@type": "ListItem",
            "position": 2,
            "name": "{{ 'Thi Online' }}",
            "item": "{{ route( 'front.home.search-choice') }}"
        }@endif]
    }
    </script>
@endsection

@push('css')
    <link rel="stylesheet" href="{{ mix_cdn('css/frontend/exam-preview.css') }}" />
@endpush

@section('content')
<div class="main-page" data-micon="{{ $course->getClassLevel->icon ?? '' }}">
    @if ($seasons->count() > 0)
        @include('nqadmin-course::frontend.lecture.tests.partials.lsidebar')
    @endif
    <div class="box-wrapper">
        <div class="box-wrapper-header bg-white bb-1">
            <div class="container">
                <div class="breadcrumb-wrapper">
                    <ol class="breadcrumb">
                        @if ($course->getClassLevel)
                            <li>
                                <a class="has-underline"
                                    href="{{ route('front.home.search-choice.class', [
                                        'slug' => $course->getClassLevel->slug,
                                        'type' => $course->type,
                                        'book' => $course->book_id,
                                    ]) }}"
                                >
                                    {{ $course->getClassLevel->name }}
                                </a>
                            </li>
                            @if ($course->getSubject)
                                <li>
                                    <a class="has-underline"
                                        href="{{ route('home.exam.class-subject', [
                                            'classSlug' => $course->getClassLevel->slug,
                                            'subjectSlug' => $course->getSubject->slug,
                                            'type' => $course->type,
                                            'book' => $course->book_id,
                                        ]) }}"
                                    >
                                        {{ $course->getSubject->name }}
                                    </a>
                                </li>
                            @endif
                        @else
                            <li>
                                <a class="has-underline" href="{{ route('front.home.index.get') }}">Trang chủ</a>
                            </li>
                        @endif
                        <li class="active">
                            <a href="{{ route('front.exam.get', ['slug' => $course->slug]) }}" class="has-underline">
                                {{ $course->name }}
                            </a>
                        </li>
                    </ol>
                </div>
                <div class="row">
                    <div class="col-md-8">
                        <h1 class="name-exam">
                            {{ $lecture->name }}
                            @if (Auth::check() && Auth::user()->can('course_index'))
                                <a href="{{route('nqadmin::course.curriculum.get', ['id' => $course->id])}}" class="btn btn-link" target="_blank">
                                    <i class="fa fa-user-edit"></i>
                                </a>
                            @endif
                        </h1>
                        <p>
                            @if ($lecture->count2week > 0)
                            <span class="mr-15 text-muted">
                                <i class="fas fa-chart-line" style="color: #f6406c;"></i>
                                {{ $lecture->count2week }} người thi tuần này
                            </span>
                            @endif
                            <span class="mr-15 text-muted">
                                <i class="fa fa-star" style="color: #FF9800;"></i>
                                {{ number_format($averageRating, 1) }}
                            </span>
                            <span class="mr-15 text-muted"><i class="far fa-calendar-check"></i> {{ number_format_short($course->view) }} lượt thi</span>
                            <span class="mr-15 text-muted"><i class="far fa-question-circle"></i> {{ $allQuestionCount }} câu hỏi</span>
                            @if ($lecture->time)
                            <span class="mr-15 text-muted"><i class="far fa-clock"></i> {{ $lecture->time }} phút</span>
                            @endif
                        </p>
                        @if ($course->related_lectures_count > 0)
                            <ul class="list-inline tab-preview pc">
                                <li class="active">
                                    <button type="button" class="btn btn-default">
                                        <i class="far fa-calendar-check"></i>
                                        <span>{{ $course->curriculumExam->count() }} đề thi</span>
                                    </button>
                                </li>
                                <li>
                                    <a href="{{ route('front.lecture.get', ['slug' => $course->slug]) }}" class="btn btn-default">
                                        <i class="far fa-play-circle"></i>
                                        <span>{{ $course->related_lectures_count }} Video</span>
                                    </a>
                                </li>
                            </ul>
                        @endif
                    </div>
                    <div class="col-md-4">
                        <div
                            id="initShareQuiz"
                            data-auth="{{ auth()->check() }}"
                            data-examid="{{ $lecture->id }}"
                            data-title="{{ $lecture->name }}"
                            data-liked="{{ $trackingView->like ?? null }}"
                        ></div>
                    </div>
                </div>
            </div>
            @if ($course->related_lectures_count > 0)
                <ul class="nav nav-tabs tab-preview mb">
                    <li class="active">
                        <a>
                            <i class="far fa-calendar-check"></i>
                            <span>{{ $course->curriculumExam->count() }} đề thi</span>
                        </a>
                    </li>
                    <li>
                        <a href="{{ route('front.lecture.get', ['slug' => $course->slug]) }}">
                            <i class="far fa-play-circle"></i>
                            <span>{{ $course->related_lectures_count }} Video</span>
                        </a>
                    </li>
                </ul>
            @endif
            <div class="bt-1">
                <div class="container p-sm-0">
                    <div class="scroll-slider scroll-off">
                        <span class="scroll-overlay"><i class="fas fa-angle-left"></i></span>
                        <span class="scroll-overlay"><i class="fas fa-angle-right"></i></span>
                        <div class="scroll-on">
                            <ul class="tab-exam">
                                @foreach($course->curriculumExam as $key => $curriculum)
                                    <li class="{{ $lecture->id ==  $curriculum->id ? 'active' : '' }}">
                                        <a title="{{ $curriculum->name }}" href="{{ route('front.exam.get', [
                                            $course->slug,
                                            $curriculum->id
                                        ]) }}">Đề số {{ $key + 1 }}</a>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="box-wrapper-body container">
            @if ($allQuestionCount > 0)
                @push('js')
                <script>
                    window.INIT_QUES = @json($lecture->getAllQuestion);
                    window.INIT_PARAGRAPHS = @json(!empty($lecture->quiz_content) ? $lecture->quiz_content : []);
                </script>
                <script src="{{ mix_cdn('js/modules/quiz.js') }}"></script>
                @endpush
                <div id="initQuiz"
                    class="quiz-wrapper"
                    data-auth="{{ auth()->check() }}"
                    data-action="{{ route('nqadmin::course.multi-choice.start', [
                        'slug' => $course->slug,
                        'lectureId' => $lecture->id
                    ]) }}"
                    data-vip="{{ $checkVip }}"
                    @if (!$checkVip)
                    data-buyviphtml="{{ htmlspecialchars(view('nqadmin-course::frontend.lecture.tests.partials.buy-vip', ['orderPackage' => $orderPackage])->render(), ENT_QUOTES, 'UTF-8') }}"
                    @endif
                    @if ($course->related_lectures_count > 0)
                    data-lectureaction="{{ route('front.lecture.get', ['slug' => $course->slug]) }}"
                    @endif
                ></div>
            @else
                <div class="quiz-wrapper">
                    <p class="mt-15 text-center">Đang cập nhật ...</p>
                </div>
            @endif
            @if ($course->related_lectures_count > 0)
                <div class="rsidebar">
                    <div class="rsidebar-wrapper">
                        <div class="rsidebar-header">
                            Bài giảng / Lý thuyết:
                        </div>
                        <div class="rsidebar-body">
                            <div class="row">
                                @foreach($course->relatedLectures as $key => $relatedLecture)
                                <div class="col-md-12">
                                    <div class="p-item" style="padding: 5px;">
                                        <i class="fas fa-play-circle" style="font-size: 17px; color: #E91E63;"></i>
                                        <a href="{{ route('front.lecture.get', [
                                            $course->slug,
                                            $relatedLecture->id
                                        ]) }}" class="text-underline">
                                            <p style="font-size: 15px; color: #0e71bf;">
                                                {{ $relatedLecture->name }}
                                            </p>
                                        </a>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </div>
        <div class="box-wrapper-footer mt-30 mb-30">
            <div class="container">
                @include('nqadmin-dashboard::frontend.components.hometop.hotexamsV2')
                @if ($seasons->count() > 0)
                    @php
                        $seasonCur = $seasons->where('id', $course->season_id)->first();
                        $otherCourses =  $seasonCur ?
                            ($seasonCur->lessons->count() > 0 ?
                                $seasonCur->lessons->pluck('courses')->flatten(1)->take(10) :
                                $seasonCur->courses->take(10)
                            ) : collect([]);
                    @endphp
                    @if ($otherCourses->count() > 1)
                        <h4 class="meta-title mt-30">Nội dung liên quan:</h4>
                        <div class="media-group">
                            @foreach($otherCourses->where('id', '<>', $course->id) as $otherCourse)
                            <div class="panel ex-item">
                                <div class="panel-body">
                                    <a href="{{ route('front.exam.get', ['slug' => $otherCourse->slug]) }}">
                                        <h3>{{ $otherCourse->name }}</h3>
                                    </a>
                                    <div>
                                        <span class="small mr-15" style="color: #FF9800;">
                                            <i class="fa fa-star"></i>
                                            <i class="fa fa-star"></i>
                                            <i class="fa fa-star"></i>
                                            <i class="fa fa-star"></i>
                                            <i class="fa fa-star"></i>
                                        </span>
                                        <span class="mr-15 text-muted"><i class="far fa-calendar-check"></i> {{ $otherCourse->view }} lượt thi</span>
                                    </div>
                                    <div class="ex-action mt-10">
                                        <a href="{{ route('front.exam.get', ['slug' => $otherCourse->slug]) }}" class="btn btn-default" style="color: #FF5722;">
                                            <i class="far fa-calendar-check"></i> {{ $otherCourse->exam_count }} đề
                                        </a>
                                        @if ($otherCourse->lecture_count)
                                        <a href="{{ route('front.lecture.get', ['slug' => $otherCourse->slug]) }}" class="btn btn-default" style="color: #009688;">
                                            <i class="fas fa-play-circle"></i> {{ $otherCourse->lecture_count }} video
                                        </a>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    @endif
                @endif
                @if ($allQuestionCount > 0)
                    <h4 class="meta-title mt-30">Danh sách câu hỏi:</h4>
                    <div class="qas">
                        @if (!empty($lecture->quiz_content))
                            @php
                                $groupedQuestions = $lecture->getAllQuestion->groupBy('text_select');
                                $number = 0;
                            @endphp
                            @foreach ($groupedQuestions as $textSelect => $listQuestions)
                                @if (!empty($lecture->quiz_content[$textSelect]['content']))
                                    <p class="text-center">
                                        <span style="background-color: #ffff00; color: #000; font-weight: 500;">
                                            Đoạn văn {{ $textSelect + 1 }}
                                        </span>
                                    </p>
                                    <div class="mb-20 is-paragraph">
                                        {!! $lecture->quiz_content[$textSelect]['content'] !!}
                                    </div>
                                @endif
                                @foreach ($listQuestions as $question)
                                    @include('nqadmin-course::frontend.lecture.tests.partials.question-preview', [
                                        'number' =>  $number + 1,
                                        'showReason' => $checkVip || (!$checkVip && $number < config('web.question_free')),
                                    ])
                                    @php
                                        $number++;
                                    @endphp
                                @endforeach
                            @endforeach
                        @else
                            @foreach($lecture->getAllQuestion as $number => $question)
                                @include('nqadmin-course::frontend.lecture.tests.partials.question-preview', [
                                    'number' =>  $number + 1,
                                    'showReason' => $checkVip || (!$checkVip && $number < config('web.question_free')),
                                ])
                            @endforeach
                        @endif
                    </div>
                @endif
            </div>
        </div>
        @if ($seasons->count() > 0)
        <div class="navigation-mb">
            <button type="button" class="btn btn-warning toggle-lsidebar">
                <i class="fas fa-list-ul mr-5"></i>
                <span>Danh sách bài học</span>
            </button>
        </div>
        @endif
    </div>
</div>
@endsection
