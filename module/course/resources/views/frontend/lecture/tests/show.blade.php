@extends('nqadmin-dashboard::frontend.master')

@push('css')
    <link rel="stylesheet" href="{{ mix_cdn('css/frontend/qa-page.css', true, config('app.asset_cdn')) }}" />
@endpush

@php
    $seo_description = html_clean($question->content);

    foreach ($question->getAnswer as $answer) {
        $seo_description .= ' ' . html_clean($answer->content);
    }

    $title = text_clean($question->title) ?: str_limit($seo_description, 140);
    $ratingCount = ratingCount(0, $question->view) ?: config('rating.rating_count_default');
    $text = html_clean($question->reason);
    $description = str_limit($seo_description, 200);

    if (in_array($text, ['', '.', ',', ' '])) {
        $acceptedAnswer = $question->getAnswer->where('answer', \MultipleChoices\Models\Answer::RIGHT)->first();

        if ($acceptedAnswer) {
            $text = html_clean($acceptedAnswer->content);

            if (in_array($text, ['', '.', ',', ' '])) {
                $text = config('rating.text_default');
            }
        } else {
            $text = config('rating.text_default');
        }
    }
@endphp

@section('title', $title)
@section('seo_title', $title . ' (Miễn phí)')
@section('seo_description', $seo_description)
@section('seo_keywords', 'Đề thi, thi thử, thi thử THPT Quốc Gia, đề thi thử, Toán học, vật lý, hóa học, sinh học, Tiếng Anh, Ngữ Văn. Lớp 10, lớp 11, lớp 12, TOEIC, IELTS')
@section('og_title', $title . ' (Miễn phí)')
@section('og_description', $seo_description)
@section('og_image', isset($course->getLdp->thumbnail) ? asset($course->getLdp->thumbnail) : asset_cdn('images/course-df-thumbnail.jpg'))
@section('og_type', 'article')
@section('showChildMenuClass', 'hide')

@section('snippet')
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [{
            "@type": "ListItem",
            "position": 1,
            "name": "Trang chủ",
            "item": "{{ route('front.home.index.get') }}"
        }@if($course->getClassLevel),{
            "@type": "ListItem",
            "position": 2,
            "name": "{{ $course->getClassLevel->name }}",
            "item": "{{ route('front.home.search-choice.class', [
                'slug' => $course->getClassLevel->slug,
                'type' => $course->type,
                'book' => $course->book_id,
            ]) }}"
        }@endif
        @if($course->getClassLevel && $course->getSubject),{
            "@type": "ListItem",
            "position": 3,
            "name": "{{ $course->getSubject->name }}",
            "item": "{{ route('home.exam.class-subject', [
                'classSlug' => $course->getClassLevel->slug,
                'subjectSlug' => $course->getSubject->slug,
                'type' => $course->type,
                'book' => $course->book_id,
            ]) }}"
        }@endif]
    }
    </script>
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "AggregateRating",
        "ratingValue": "{{ config('rating.avg_default') }}",
        "ratingCount": "{{ $ratingCount }}",
        "itemReviewed": {
            "@type": "CreativeWorkSeries",
            "name": "{{ $title }}",
            "image": ""
        }
    }
    </script>
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "QAPage",
        "name": "{{ $title }}",
        "description": "{{ $description }}",
        "mainEntity": {
            "@type": "Question",
            "@id": "{{ route('front.question.preview.get', [$question->id, $question->slug]) }}",
            "name": "{{ $title }}",
            "text": "{{ $description }}",
            "dateCreated": "{{ $question->updated_at }}",
            "answerCount": "1",
            "author": {
                "@type": "Person",
                "name": "VietJack"
            },
            "acceptedAnswer": {
                "@type": "Answer",
                "upvoteCount": "{{ $ratingCount }}",
                "text": "{{ $text }}",
                "url": "{{ route('front.question.preview.get', [$question->id, $question->slug]) . '/#acceptedAnswer' }}",
                "dateCreated": "{{ $question->updated_at }}",
                "author": {
                    "@type": "Person",
                    "name": "VietJack"
                }
            },
            "suggestedAnswer": []
        }
    }
    </script>
@endsection

@section('content')
    <div class="container p-sm-0 mt-20">
        @if (!(Auth::check() && Auth::user()->isVip))
            @if (!in_array($question->id, [16872, 17544, 53036]) && !Agent::isMobile())
                <div class="box-adv text-center adv-center mb-30">
                    <ins class="982a9496" data-key="21c9ae6876fbe7bb9710d01e034f6a8f"></ins>
                    <script async defer src="//aj1559.online/ba298f04.js"></script>
                </div>
            @endif
        @endif
        <div class="main-page" data-micon="{{ $course->getClassLevel->icon ?? '' }}">
            <div class="box-wrapper">
                <div class="main-qa">
                    <div class="left-qa">
                        <div class="bg-white br-5">
                            <div class="title-qa">
                                <div class="breadcrumb-wrapper">
                                    <ol class="breadcrumb">
                                        <li>
                                            <a class="has-underline" href="{{ route('front.home.index.get') }}"><i class="fas fa-home"></i></a>
                                        </li>
                                        @if ($course->getClassLevel)
                                            <li>
                                                <a class="has-underline"
                                                    href="{{ route('front.home.search-choice.class', [
                                                        'slug' => $course->getClassLevel->slug,
                                                        'type' => $course->type,
                                                        'book' => $course->book_id,
                                                    ]) }}"
                                                >
                                                    {{ $course->getClassLevel->name }}
                                                </a>
                                            </li>
                                            @if ($course->getSubject)
                                                <li>
                                                    <a class="has-underline"
                                                        href="{{ route('home.exam.class-subject', [
                                                            'classSlug' => $course->getClassLevel->slug,
                                                            'subjectSlug' => $course->getSubject->slug,
                                                            'type' => $course->type,
                                                            'book' => $course->book_id,
                                                        ]) }}"
                                                    >
                                                        {{ $course->getSubject->name }}
                                                    </a>
                                                </li>
                                            @endif
                                        @endif
                                    </ol>
                                </div>
                                <div class="question-meta">
                                    <p class="title-des">
                                        Câu hỏi:
                                    </p>
                                    <span class="question-info">
                                        <span><i class="fas fa-clock"></i> {{ formatDateString($question->updated_at) }}</span>
                                        <span><i class="fas fa-eye"></i> {{ number_format($question->view) }}</span>
                                        <a class="btn btn-outline-like {{ Auth::check() ? 'js-btn-like' : 'btn-popup' }}" data-tracking-id="{{ $question->id }}" data-type="question" href="#login-box">
                                            {!! $trackingView && $trackingView->like == 1 ? '<i class="fas fa-heart heart-color mr-2"></i> Đã lưu' : '<i class="far fa-heart mr-2"></i> Lưu' !!}
                                        </a>
                                    </span>
                                </div>
                                <div class="question">
                                    <h1 class="title-question overflow-x-el">
                                        @if ($quizContent)
                                            <div class="mb-20 is-paragraph">
                                                {!! $quizContent !!}
                                            </div>
                                        @endif
                                        {!! $question->content !!}
                                    </h1>
                                    @if (!(Auth::check() && Auth::user()->isVip))
                                        @if (Agent::isMobile())
                                            <div class="box-adv bg-adv border-adv-top text-center">
                                                <p class="label-adv" style="margin: 0;">
                                                    <span>Quảng cáo</span>
                                                </p>
                                                <ins class="982a9496" data-key="b98bd485f037a2ee590d40d41e3d1ee4"></ins>
                                                <script async src="https://aj1559.online/ba298f04.js"></script>
                                            </div>
                                        @else
                                            <div class="box-adv text-center">
                                                <ins class="982a9496" data-key="672ef475dd91ce81f54582047e8c7806"></ins>
                                                <script async src="https://aj1559.online/ba298f04.js"></script>
                                            </div>
                                        @endif
                                    @endif
                                    @if ($question->type == 'test')
                                    <div class="answer-check radio" data-question={{ $question->id }}>
                                        @foreach($question->getAnswer as $answer)
                                            <div
                                                class="option-choices js-answer"
                                                data-answer="{{ $answer->answer }}"
                                            >
                                                <label class="option-content overflow-x-el">
                                                    {!! $answer->content !!}
                                                </label>
                                            </div>
                                        @endforeach
                                    </div>
                                    @endif
                                    <div class="text-center"><a class="viewreason" data-toggle="scroll" data-target="#reason"><i class="far fa-hand-point-down"></i> Xem lời giải</a></div>
                                </div>
                            </div>
                            <div class="title-exam">
                                <p>
                                    <span style="font-size: 16px;">Câu hỏi trong đề:</span>
                                    <a class="link-title_qa has-underline" href="{{ route('front.exam.get', [
                                        $course->slug,
                                        $lecture->id
                                    ]) }}">
                                        {{ $course->name }} !!
                                    </a>
                                </p>
                                <div class="custom-btn-wrapper">
                                    <a class="btn-start btn-start-exam" href="{{ route('front.exam.get', [
                                        $course->slug,
                                        $lecture->id
                                    ]) }}">
                                        <i class="far fa-clock mr-2"></i> Bắt đầu thi
                                    </a>
                                    @if (Auth::check())
                                    <div class="dropdown">
                                        <button
                                        id="dropdownDownloadButton"
                                        class="btn btn-down dropdown-toggle w-100"
                                        data-toggle="dropdown"
                                        aria-haspopup="true"
                                        aria-expanded="false"
                                        data-title="{{ $lecture->name }}"
                                        data-eid="{{ $lecture->id }}"
                                        >
                                            <i class="fas fa-download mr-2"></i> <span class="text-lg-none">In đề / </span>Tải về
                                        </button>
                                        <ul class="dropdown-menu" aria-labelledby="dropdownDownloadButton" style="left: auto; right: 0;">
                                            <li><button class="btn btn-link js-btn-edown" data-type="quiz"><i class="fas fa-file-download mr-5"></i> Tải xuống đề thi</button></li>
                                            <li><button class="btn btn-link js-btn-edown" data-type="answer"><i class="fas fa-file-alt mr-5"></i> Tải xuống đáp án đề thi</button></li>
                                            <li><button class="btn btn-link js-btn-edown" data-type="full"><i class="fas fa-file-invoice mr-5"></i> Tải xuống đề thi kèm đáp án chi tiết</button></li>
                                        </ul>
                                    </div>
                                    @else
                                    <a class="btn btn-down btn-popup w-100" href="#login-box">
                                        <i class="fas fa-download mr-2"></i> <span class="text-lg-none">In đề / </span>Tải về
                                    </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                        @if (!(Auth::check() && Auth::user()->isVip))
                            @if (!Agent::isMobile())
                            <div class="box-adv text-center mt-15">
                                <p class="label-adv" style="margin: 0;">
                                    <span>Quảng cáo</span>
                                </p>
                                <ins class="982a9496" data-key="49c7944c45f4bb06a031d4968dfd2b50"></ins>
                                <script async src="https://aj1559.online/ba298f04.js"></script>
                            </div>
                            @endif
                        @endif
                        <div class="bg-white border-simple answer-container mt-30" id="reason">
                            <div class="answer">
                                <div class="question-header">
                                    <div class="question-heading">
                                        <p class="title-des"> Trả lời:</p>
                                    </div>
                                    <div class="question-verified-box">
                                        <img src="{{ asset_cdn('images/verified.webp') }}" width="18" height="21" alt="verified">
                                        <span class="question-text">Giải bởi Vietjack</span>
                                    </div>
                                </div>
                                <div class="question">
                                    @if ($question->getAnswer->count() > 0)
                                        @switch($question->type)
                                            @case('essay')
                                                <p class="text-warning">Đáp án:</p>
                                                <div class="option-essay">
                                                    {{ $question->getAnswer->first()->content }}
                                                </div>
                                                @break

                                            @case('blank')
                                                <div class="options-blank">
                                                    <p class="mb-0 text-warning">Đáp án:</p>
                                                    @foreach($question->getAnswer as $key => $answer)
                                                        <div class="option-blank">
                                                            <span class="mr-2">{{ $key + 1 }}.</span> {{ $answer->content }}
                                                        </div>
                                                    @endforeach
                                                </div>
                                                @break
                                        @endswitch
                                    @endif
                                    <div class="result">
                                        {!! $question->reason !!}
                                    </div>
                                </div>
                            </div>
                            @if ($relatedQuestion->count() > 1)
                                <p class="q-separator mt-4">
                                    <strong class="text-danger">
                                        <i class="fa fa-list ml-0"></i> Câu hỏi cùng đoạn
                                    </strong>
                                </p>
                                @foreach($relatedQuestion->where('id', '<>', $question->id) as $key => $que)
                                    <div class="mb-20" style="border-bottom: 1px dashed #dee2e6;">
                                        <p class="title-des3">Câu {{ $key + 1 }}:</p>
                                        <div class="item-qa question">
                                            <h3 class="title-question overflow-x-el limit-content" style="--max-h: {{config('web.h_limitq')}}">
                                                {!! $que->content !!}
                                            </h3>
                                            @if ($que->getAnswer->count() > 0)
                                                <div class="answer-check radio" data-question={{ $que->id }}>
                                                    @foreach($que->getAnswer as $key => $answer)
                                                        <div
                                                            class="option-choices js-answer"
                                                            data-answer="{{ $answer->answer }}"
                                                        >
                                                            <label class="option-content overflow-x-el">
                                                                {!! $answer->content !!}
                                                            </label>
                                                        </div>
                                                    @endforeach
                                                </div>
                                            @endif
                                        </div>
                                        <p class="mb-20">
                                            <a data-toggle="collapse" data-target="#reason_{{ $que->id }}" class="view-reason-btn">
                                                <i class="far fa-hand-point-right"></i> Xem lời giải
                                            </a>
                                        </p>
                                        <div id="reason_{{ $que->id }}" class="collapse reason mb-20" style="padding: 10px; font-size: 18px; border: 1px solid #f0f0f0; background: rgb(249, 249, 249);">
                                            <p class="text-left" style="color: #e29000; font-weight: 500; font-size: 15px;">
                                                <span class="question-verified-icon"><img src="{{ asset_cdn('images/verified.webp') }}" width="18" alt="verified"></span>
                                                Lời giải của GV VietJack
                                            </p>
                                            <div class="question">
                                                <div class="result">
                                                    {!! $que->reason !!}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @endif
                        </div>
                        @if (!$seasons->isEmpty())
                            @if (Agent::isMobile())
                                <div class="lsidebar-body lsidebar-small mt-30" style="height: auto; width: 100%;">
                                    @include('nqadmin-course::frontend.lecture.tests.partials.lsidebar2', ['ilimit' => 2])
                                </div>
                            @else
                                <div class="navigation-mb mt-20" style="position: relative;">
                                    <button type="button" class="btn btn-warning toggle-lsidebar">
                                        <i class="fas fa-list-ul mr-5"></i>
                                        <span>Danh sách đề thi</span>
                                    </button>
                                </div>
                            @endif
                        @endif
                        @if (!empty($products) || !empty($configProducts['productText']))
                            <div class="pbox-products mt-30">
                                @if (!empty($configProducts['productText']))
                                    {!! $configProducts['productText']->content !!}
                                @endif
                                <div class="pbox-header">
                                    <div class="pbox-title">
                                        Sách thầy cô Vietjack biên soạn:
                                    </div>
                                    <div class="view-products-all">
                                        <a rel="noopener nofollow" target="_blank" href="https://shopee.vn/vietjack_official_store" style="color: #607D8B; font-size: 14px; font-weight: 500;">Xem thêm »</a>
                                    </div>
                                </div>
                                @if ($products->count() > 0)
                                {{-- <div class="mt-15">
                                    @include('nqadmin-course::frontend.lecture.tests.partials.products')
                                </div> --}}
                                <div class="pbox-body">
                                    <ol class="pbox-store">
                                        @foreach($products as $product)
                                        <li>
                                            <a rel="noopener nofollow" href="{{ $product->origin_link }}" target="_blank">{{ $product->name }} <span class="price-store">( {{ number_format((int)$product->price, 0, ',', '.') }}₫ )</span> </a>
                                        </li>
                                        @endforeach
                                    </ol>
                                </div>
                                @endif
                            </div>
                        @endif
                        @if (Agent::isMobile())
                            <div class="mt-15 text-center">
                                <div class="alert alert-warning">
                                    <a class="js-download_app open-in-app" style="color: #FF5722;">
                                        <i class="fas fa-mobile-alt fa-question-circle22"></i>
                                        Hỏi bài trong APP VIETJACK
                                    </a>
                                </div>
                            </div>
                            <div class="mt-20">
                                @include('nqadmin-dashboard::frontend.components.hometop.hotexamsV2')
                            </div>
                        @endif
                        <div class="bg-white mt-30">
                            @include('nqadmin-dashboard::frontend.components.comments.index', [
                                'choice_id' => $question->id,
                            ])
                        </div>
                    </div>
                    <p class="q-separator">
                        <span class="text-danger" style="background-color: #e8eaed; font-size: 18px; color: #458ea7; font-weight: 800;">
                            CÂU HỎI HOT CÙNG CHỦ ĐỀ
                        </span>
                    </p>
                    @foreach($lecture->getLimitQuestion->where('id', '<>', $question->id) as $que)
                        @include('nqadmin-course::frontend.lecture.tests.partials.question-preview', [
                            'number' =>  $loop->iteration,
                            'showReason' => $checkVip || (!$checkVip && $loop->iteration < 3),
                            'question' => $que,
                        ])
                    @endforeach
                    <div class="text-center mb-20 mt-20">
                        <a class="text-more" href="{{ route('front.exam.get', [
                            $course->slug,
                            $lecture->id
                        ]) }}">
                            Xem thêm các câu hỏi khác »
                        </a>
                    </div>
                </div>
            </div>
            @if ($seasons->isEmpty())
            <aside class="lsidebar bg-transparent border-0 bottom-sticky-position" id="lsidebar">
                <div class="lsidebar-main">
                    <div class="lsidebar-wrapper">
                        <div class="lsidebar-body" style="height: auto;">
                            <div class="mb-20">
                                @include('nqadmin-course::frontend.lecture.tests.partials.box-vip', [
                                    'hiddenDesVip' => true,
                                ])
                            </div>
                            <div class="mb-20 text-center">
                                <a class="btn-start btn-block" href="https://hoidapvietjack.com/" target="_blank">
                                    <i class="fas fa-question-circle"></i>
                                    Hỏi bài tập
                                </a>
                            </div>
                            <div class="bg-white br-5 mb-20" style="padding-bottom: 20px;">
                                <h4 class="title-list m-0" style="padding: 10px 10px 5px;"><span>🔥</span> Đề thi HOT:</h4>
                                <div class="overflow-y-el" style="max-height: 500px;">
                                    <ol class="exam-hot-list">
                                        @foreach($hotExams as $hotExam)
                                        <li class="exam-hot-item">
                                            <a href="{{ route('front.exam.get', [$hotExam->slug, $hotExam->id]) }}" class="w-100">
                                                <div class="l-item">
                                                    <div class="l-content">
                                                        @if ($hotExam->count2week > 0)
                                                            <div class="p-meta mr-10"><i class="fas fa-chart-line"></i> {{ $hotExam->count2week }} người thi tuần này</div>
                                                        @endif
                                                        <p class="l-subtitle mb-0">{{ $hotExam->name }}</p>
                                                        {{-- <div class="l-meta">
                                                            <span><i class="far fa-question-circle"></i> {{ $hotExam->curriculum_questions_count ?: 'x' }} câu hỏi</span>
                                                            <span><i class="far fa-clock"></i> {{ number_format_short($hotExam->view + $hotExam->count2week) }} lượt thi</span>
                                                        </div> --}}
                                                    </div>
                                                </div>
                                            </a>
                                        </li>
                                        @endforeach
                                        <li><br></li>
                                    </ol>
                                </div>
                                <div class="footer-list"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </aside>
            @elseif (!Agent::isMobile())
            <aside class="lsidebar bg-transparent border-0" id="lsidebar">
                <div class="lsidebar-main mb-20" style="top: 70px;">
                    <div class="lsidebar-wrapper">
                        <button class="btn btn-default btn-lsidebar toggle-lsidebar show-mobile" type="button">
                           <i class="fas fa-indent"></i>
                        </button>
                        <div class="lsidebar-body" style="height: calc(100vh - 70px);">
                            <div class="mb-20 text-center hide-mobile">
                                <a class="btn-start btn-block" href="https://hoidapvietjack.com/" target="_blank">
                                    <i class="fas fa-question-circle"></i>
                                    Hỏi bài tập
                                </a>
                            </div>
                            @include('nqadmin-course::frontend.lecture.tests.partials.lsidebar2', ['ilimit' => 5])
                        </div>
                    </div>
                </div>
            </aside>
            @endif
        </div>
    </div>
@endsection

@section('js')
    @if (!(Auth::check() && Auth::user()->isVip))
        @if (!in_array($question->id, [16872, 17544, 53036]))
            @if (!Agent::isMobile())
                <ins class="982a9496" data-key="c8a4baa4fb20e0400455991ac0803ada"></ins>
                <script async defer src="https://aj1559.online/ba298f04.js"></script>
            @endif

            <script async defer src="//aj1559.online/ba298f04.js"></script>

            <ins class="982a9496" data-key="fa32779895fc725fd597ef0b5bb0707b"></ins>
            <script async defer src="https://aj1559.online/ba298f04.js"></script>
        @endif
    @endif
    <script src="{{ asset_cdn('libs/affs.js') }}?id=4"></script>
@endsection
@push('js-head')
    <script>
        var sLinkAff = 'https://s.shopee.vn/702mDOpnWt';
    </script>
@endpush
