@php
    $seo_title = $subject->name . ' ' . str_replace('Lớp ', '', $class->name);
    $canonical_params = [];

    if (isset($chapter)) {
        $seo_title = $chapter->name . ' - ' . $seo_title;
    }

    switch (request('type')) {
        case 'essay':
            $seo_title = ($examCounts->essay_count ?: 'Tổng hợp') .  ' bài tập ' . $seo_title;
            $canonical_params['type'] = 'essay';
            break;
        default:
            $seo_title = ($examCounts->test_count ?: 'Tổng hợp') . ' đề thi ' . $seo_title;
            $canonical_params['type'] = 'test';
    }

    if (!empty($book)) {
        $seo_title .= (' ' . $book->name);
        $canonical_params['book'] = $book->id;
    }

    $seo_description = $seo_title . '  với đầy đủ các dạng từ cơ bản đến nâng cao có đáp án và lời gi<PERSON>i chi tiết.';
    $seo_title = $seo_title . ' có đáp án mới nhất ' . date('Y');
    $canonical_url = replace_base_url(url()->current());

    if (!empty($canonical_params)) {
        $canonical_url .= '?' . http_build_query($canonical_params);
    }
@endphp

@section('title', $seo_title)
@section('og_title', $seo_title)
@section('seo_description', $seo_description)
@section('seo_keywords', 'VietJack, thi online, trắc nghiệm, tự luận, Toán, Lý, Hóa, Sinh, Tiếng Anh, Sử, Địa, GDCD')
@section('og_description', $seo_description)
@section('canonical', $canonical_url)

@extends('nqadmin-dashboard::frontend.master')

@section('content')
    <div class="main-page" data-micon="{{ $class->icon }}">
        <div class="container">
            <div class="row list-exam">
                <div class="col-xs-12 col-sm-8 col-md-9">
                    <div class="mt-30 mb-30">
                        @include('nqadmin-course::frontend.course.exam.partials.exam-subject')
                    </div>
                    <div class="mb-30">
                        @include('nqadmin-dashboard::frontend.components.hometop.hotexamsV2')
                    </div>
                    <div class="mb-20">
                        @include('nqadmin-course::frontend.course.exam.partials.exam-title')
                        @include('nqadmin-course::frontend.course.exam.partials.exam-tabfilter')
                        <div class="exam-content">
                            @include('nqadmin-course::frontend.course.exam.partials.exam-tree')
                        </div>
                    </div>
                </div>
                <div class="col-xs-12 col-sm-4 col-md-3 bottom-sticky-position">
                    <div class="mt-30 mb-30">
                        @include('nqadmin-course::frontend.lecture.tests.partials.box-vip')
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
