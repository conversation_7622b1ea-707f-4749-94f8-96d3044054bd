<?php

namespace Course\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use Book\Models\Book;
use Cart\Models\Order;
use Level\Models\Level;
use Media\Models\Media;
use Qa\Models\Question;
use Users\Models\Users;
use Coupon\Models\Coupon;
use Season\Models\Season;
use Season\Models\Lesson;
use Rating\Models\Rating;
use Subject\Models\Subject;
use Cart\Models\OrderDetail;
use Course\Models\TestResult;
use Setting\Models\CourseType;
use Advertise\Models\Advertise;
use Base\Models\TrackingTryStudy;
use ClassLevel\Models\ClassLevel;
use Base\Models\TrackingAddToCart;
use Course\Models\CourseCurriculumItems;
use MultipleChoices\Models\Question as QuestionTest;
use Illuminate\Support\Facades\Auth;
use Staudenmeir\EloquentEagerLimit\HasEagerLimit;

class Course extends Model
{
    use HasEagerLimit;

    const TYPES = [
        'normal' => 'normal', // Kh<PERSON>a học bài giảng
        'test' => 'test', // <PERSON><PERSON> thi trắc nghiệm
        'essay' => 'essay', // <PERSON><PERSON> thi Tự luận
    ];

    const FREE_DEFAULT = 0;
    const FREE_ACTIVE = 1;
    const PATTERN_BASIC = 1;
    const PATTERN_ADVANCED = 2;

    protected $table = 'course';
    protected $guarded = [];
    // protected $fillable = [
    //     'name',
    //     'slug',
    //     'old_slug',
    //     'author',
    //     'editor',
    //     'status',
    //     'created_at',
    //     'updated_at',
    //     'published_at',
    //     'price',
    //     'approve_sale_system',
    //     'type',
    //     'google_analytics_id',
    //     'assistant',
    //     'time_start',
    //     'time_end',
    //     'classlevel',
    //     'subject',
    //     'book_id',
    //     'season_id',
    //     'lesson_id',
    //     'level',
    //     'sort',
    //     'company',
    //     'group',
    //     'count2week',
    //     'trending',
    //     'trending_index',
    //     'pattern',
    //     'course_type_id',
    //     'curriculums_count',
    //     'related_lectures_count',
    //     'with_vip',
    // ];

    public static function tableName(): string
    {
        return (new static)->getTable();
    }

    public function setPublishedAtAttribute($value)
    {
        $published_at = strtotime(str_replace('/', '-', $value));
        $published_at = Carbon::createFromTimestamp($published_at);
        $this->attributes['published_at'] = $published_at;
    }

    public function setTimeStartAttribute($value)
    {
        $published_at = strtotime(str_replace('/', '-', $value));
        $published_at = Carbon::createFromTimestamp($published_at);
        $this->attributes['time_start'] = $published_at;
    }

    public function setTimeEndAttribute($value)
    {
        $published_at = strtotime(str_replace('/', '-', $value));
        $published_at = Carbon::createFromTimestamp($published_at);
        $this->attributes['time_end'] = $published_at;
    }

    public function getFullNameAttribute()
    {
        return "{$this->first_name} {$this->last_name}";
    }

    // lay so gio hoc
    public function getDurationAttribute()
    {
        $duration = 0;
        foreach ($this->getCurriculum as $item) {
            foreach ($item->getChildCurriculum as $value) {
                foreach ($value->getMedia as $media) {
                    $duration += $media->duration;
                }
            }
        }
        return $duration;
    }

    public function getSectionAttribute()
    {
        $sections = 0;
        foreach ($this->getCurriculum as $item) {
            foreach ($item->getChildCurriculum as $value) {
                if (!empty($value->getMedia)) {
                    $sections++;
                }
            }
        }

        return $sections;
    }

    public function getProcessFinishAttribute()
    {
        $process = 0;
        foreach ($this->getCurriculum as $item) {
            foreach ($item->getChildCurriculum as $value) {
                if (!empty($value->getProcess) && $value->getProcess->status == 3) {
                    $process++;
                }
            }
        }

        return $process;
    }

    public function getProcessAttribute()
    {
        $sections = 0;
        $process = 0;
        foreach ($this->getCurriculum as $item) {
            foreach ($item->getChildCurriculum as $value) {
                if (!empty($value->getProcess) && $value->getProcess->status == 3) {
                    $process++;
                }
                if (!empty($value->getMedia)) {
                    $sections++;
                }
            }
        }
        return $sections == 0 ? 0 : number_format($process / $sections * 100);
    }

    public function getRelateCourseByTeacherAttribute()
    {
        return $this->owner->course->where('type', 'normal')->where('status', 'active')->where('id', '!=', $this->id)->take(4);
    }

    public function getCheckTestNotMultiChoiceAttribute()
    {
        if ($this->type == 'test') {
            foreach ($this->getCurriculum as $cur) {
                foreach ($cur->getChildCurriculum as $lession) {
                    if ($lession->status == 'active') {
                        foreach ($lession->getAllQuestion as $question) {
                            if ($question->getAnswer->count() == 0) {
                                return true;
                            }
                        }
                    }
                }
            }
        }

        return false;
    }

    public function checkCoupon()
    {
        return $this->hasMany(Coupon::class, 'course', 'id')
            ->where('status', 'active')
            ->where('deadline', '>', date('Y-m-d H:i:s'))
            ->where('reamain', '>', 0)
            ->orderBy('id', 'desc');
    }

    public function getQuestionsCount()
    {
        return $this->hasManyThrough(QuestionTest::class, CourseCurriculumItems::class, 'course_id', 'curriculum_item');;
    }

    public function media()
    {
        return $this->hasManyThrough(Media::class, CourseCurriculumItems::class, 'course_id', 'curriculum_item');;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function owner()
    {
        return $this->belongsTo(Users::class, 'author');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function getEditor()
    {
        return $this->belongsTo(Users::class, 'editor');
    }

    public function courseType()
    {
        return $this->belongsTo(CourseType::class, 'course_type_id');
    }

    public function getLdp()
    {
        return $this->hasOne(CourseLdp::class, 'course_id', 'id');
    }

    public function getTarget()
    {
        return $this->hasOne(CourseTarget::class, 'course_id', 'id');
    }

    public function getCurriculum()
    {
        return $this->hasMany(CourseCurriculumItems::class, 'course_id', 'id');
    }

    public function curriculumExam()
    {
        return $this->getCurriculum()->whereIn('type', ['test', 'essay']);
    }

    public function curriculumTest()
    {
        return $this->getCurriculum()->where('type', 'test');
    }

    public function curriculumEssay()
    {
        return $this->getCurriculum()->where('type', 'essay');
    }

    public function curriculumLecture()
    {
        return $this->getCurriculum()->where('type', 'lecture');
    }

    public function getFreeCurriculum()
    {
        return $this->hasMany(CourseCurriculumItems::class, 'course_id', 'id')->where('preview', 'active');
    }

    public function getQuestionQA()
    {
        return $this->hasMany(Question::class, 'course', 'id')->orderBy('id');
    }

    public function getClassLevel()
    {
        return $this->belongsTo(ClassLevel::class, 'classlevel');
    }

    public function getLevel()
    {
        return $this->belongsTo(Level::class, 'level');
    }

    public function getSubject()
    {
        return $this->belongsTo(Subject::class, 'subject');
    }

    public function getRating()
    {
        return $this->hasMany(Rating::class, 'course', 'id')
            ->where('type', config('rating.type.course'));
    }

    public function getAdvertise()
    {
        return $this->hasMany(Advertise::class, 'course_id');
    }

    public function getOrderDetail()
    {
        return $this->hasMany(OrderDetail::class, 'course_id');
    }

    public function getOrder()
    {
        return $this->belongsToMany(Order::class, 'order_details', 'course_id', 'order_id');
    }

    public function getThumbnail()
    {
        if ($this->getLdp && file_exists(ltrim($this->getLdp->thumbnail, '/'))) {
            return $this->getLdp->thumbnail;
        }

        return 'images/course-df-thumbnail.jpg';
    }

    public function getAverageRating()
    {
        return $this->getRating->avg('rating_number') ? floor($this->getRating->avg('rating_number') * 2) / 2 : 0;
    }

    public function getYourRating()
    {
        return isset($this->getRating->where('author', Auth::id())->first()->rating_number) ? $this->getRating->where('author', Auth::id())->first()->rating_number : 0;
    }

    public function getTotalStudent()
    {
        return $this->bought + 37 * $this->getOrderDetail->where('status', 'done')->groupBy('customer')->count();
    }

    public function getStudents()
    {
        return $this->belongsToMany(Users::class, 'order_details', 'course_id', 'customer')
            ->wherePivot('status', 'done')
            ->wherePivot('price', '!=', 0)
            ->withPivot('price', 'created_at');
    }

    public function checkBought()
    {
        $return = false;

        if (Auth::check()) {
            if (Auth::user()->can('view_full_course') || Auth::id() == $this->author) {
                $return = true;
            } else {
                $order = OrderDetail::where('course_id', $this->id)
                    ->where('customer', Auth::id())
                    ->where('status', 'done')
                    ->orderBy('id', 'desc')
                    ->first();

                if ($order) {
                    $return = $order->expired_date && Carbon::parse($order->expired_date)->lte(Carbon::now()) ? false : true;
                }
            }
        }

        return $return;
    }

    public function getCurriculumProgress()
    {
        return $this->hasMany(CurriculumProgress::class, 'course_id')->where('student', Auth::id());
    }

    public function getCountFinishItem()
    {
        $return = 0;

        if (Auth::check()) {
            $return = $this->getCurriculumProgress()->where('status', 3)->count();
        }

        return $return;
    }

    public function getCountFinishItemStudent($studentId)
    {
        $return = CurriculumProgress::where('course_id', $this->id)
            ->where('student', $studentId)
            ->where('status', 3)
            ->count();

        return $return;
    }

    public function getProcessStudent($studentId)
    {
        $finish = $this->getCountFinishItem($studentId);
        $total_curriculum = $this->getCurriculum
            ->where('type', '!=', 'section')
            ->where('status', 'active')
            ->count();
        $return = $total_curriculum > 0 ? $finish / $total_curriculum * 100 : 0;

        return $return;
    }

    public function countCurriculumSection()
    {
        $count = $this->getCurriculum
            ->where('type', '!=', 'test')
            ->where('status', 'active')
            ->count();

        return $count;
    }

    public function getUserDoExam()
    {
        return $this->belongsToMany(Users::class, 'exam_user', 'exam_id', 'user_id');
    }

    public function getUserOrderCreated()
    {
        return $this->belongsToMany(Users::class, 'order_details', 'course_id', 'customer')
            ->wherePivotIn('status', ['create', 'done'])
            ->withPivot('order_id');
    }

    public function getTrackingAddToCart()
    {
        return $this->hasMany(TrackingAddToCart::class, 'course_id');
    }

    public function getTrackingTryStudy()
    {
        return $this->hasMany(TrackingTryStudy::class, 'course_id');
    }

    public function testResults()
    {
        return $this->hasManyThrough(
            TestResult::class,
            CourseCurriculumItems::class,
            'course_id',
            'curriculum_id'
        );
    }

    public function book()
    {
        return $this->belongsTo(Book::class, 'book_id');
    }

    public function season()
    {
        return $this->belongsTo(Season::class, 'season_id');
    }

    public function lesson()
    {
        return $this->belongsTo(Lesson::class, 'lesson_id');
    }

    public function parent()
    {
        return $this->belongsToMany(
            Course::class,
            'course_related',
            'related_id',
            'origin_id'
        );
    }

    public function children()
    {
        return $this->belongsToMany(
            Course::class,
            'course_related',
            'origin_id',
            'related_id'
        );
    }

    public function relatedLectures()
    {
        return $this->belongsToMany(
            CourseCurriculumItems::class,
            'lecture_test',
            'test_id',
            'lecture_id'
        )->wherePivot('type', LectureTest::TYPE_TEST_LECTURE);
    }
}
