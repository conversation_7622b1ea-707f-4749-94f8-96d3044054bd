<?php

namespace Course\Http\Controllers;

use Barryvdh\Debugbar\Controllers\BaseController;
use Carbon\Carbon;
use Cart\Repositories\OrderDetailsRepository;
use Course\Models\CourseCurriculumItems;
use Course\Models\Course;
use Course\Models\LectureTest;
use Course\Repositories\CourseCurriculumItemsRepository;
use Course\Repositories\CourseRepository;
use Course\Repositories\CurriculumProgressRepository;
use Course\Repositories\TestResultRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Media\Repositories\MediaRepository;
use MultipleChoices\Repositories\QuestionRepository;
use Qa\Repositories\QuestionRepository as Question;
use Course\Http\Resources\CourseCurriculumItemsResource;
use File;
use Cache;

class CourseCurriculumItemsController extends BaseController
{
    protected $repository;
    protected $path;
    protected $baseUrl;

    public function __construct(CourseCurriculumItemsRepository $courseCurriculumItemsRepository)
    {
        $this->repository = $courseCurriculumItemsRepository;
        $this->path = public_path() . '/';
        $this->baseUrl = env('MIX_APP_URL');
    }

    /**
     * @param \Illuminate\Http\Request $request
     *
     * @return mixed
     */
    public function getAllItems(Request $request)
    {
        $id = $request->get('cId');
        $items = $this->repository->getAllSection($id, $request);

        return CourseCurriculumItemsResource::collection($items);
    }

    /**
     * @param \Illuminate\Http\Request $request
     *
     * @return array|mixed
     */
    public function addItem(Request $request)
    {
        if (!Auth::user()->can('curriculum_create')) {
            abort(403);
        }

        $data = $request->except('userid');
        $userid = $request->get('userid');
        $newSection = $this->repository->addSection($data, $userid);

        return $newSection;
    }

    /**
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function deleteItem(
        Request $request,
        Question $question,
        MediaRepository $mediaRepository,
        QuestionRepository $questionRepository,
        TestResultRepository $testResultRepository,
        CurriculumProgressRepository $curriculumProgressRepository
    )
    {
        if (!Auth::user()->can('curriculum_delete')) {
            abort(403);
        }

        DB::beginTransaction();

        try {
            $id = $request->get('id');
            $check = CourseCurriculumItems::find($id)->load('getCourse', 'getChildCurriculum', 'getAllQuestion');

            if (!$check) {
                abort(404);
            }

            if ($check->getCourse && !Auth::user()->can('course_index_full') && $check->getCourse->author != Auth::id()) {
                abort(403);
            }

            if ($check->type == 'section' && $check->getChildCurriculum->count() > 0) {
                return [
                    'code_status' => false,
                    'message' => 'Không thể xóa khi có các rằng buộc khóa ngoại',
                ];
            }

            if ($check->type == 'test' && $check->getAllQuestion->count() > 0) {
                return [
                    'code_status' => false,
                    'message' => 'Không thể xóa khi có các rằng buộc khóa ngoại',
                ];
            }

            $curriculumProgressRepository->deleteWhere(['curriculum_id' => $id]);

            $media = $mediaRepository->scopeQuery(function ($e) use ($id) {
                return $e->where('curriculum_item', $id);
            })->all();

            foreach ($media as $m) {
                $mediaRepository->update([
                    'curriculum_item' => null
                ], $m->id);
            }

            $questionRepository->deleteWhere(['curriculum_item' => $id]);
            $questionRepository->scopeQuery(function ($e) use ($id) {
                $e->where('related_lecture', $id)->update(['related_lecture' => null]);
            });
            $testResultRepository->deleteWhere(['curriculum_id' => $id]);
            $question->deleteWhere(['lecture' => $id]);
            $this->repository->delete($id);

            if ($check->type != 'section' && $check->getCourse && $check->getCourse->curriculums_count > 0) {
                $check->getCourse->decrement('curriculums_count', 1);
            }

            DB::commit();

            return [
                'code_status' => true
            ];
        } catch (\Exception $e) {
            DB::rollBack();

            return $e->getMessage();
        }
    }

    /**
     * @param \Illuminate\Http\Request $request
     *
     * @return mixed
     */
    public function updateItem(Request $request)
    {
        if (!Auth::user()->can('curriculum_edit')) {
            abort(403);
        }

        $data = $request->all();

        if (isset($data['published_at']) && $data['published_at'] == 'Invalid date') {
            unset($data['published_at']);
        }

        $newItem = $this->repository->updateItem($data);
        $newItem['onEdit'] = false;

        return $newItem;
    }

    public function cloneItem(Request $request, MediaRepository $mediaRepository)
    {
        if ($request->id) {
            $item = CourseCurriculumItems::with('contentMedia')
                ->find($request->id);

            if ($item) {
                $newSection = $this->repository->addSection([
                    'course_id' => $request->course_id,
                    'name' => $item->name,
                    'published_at' => $item->published_at,
                    'description' => $item->description,
                    'type' => $item->type,
                    'status' => $item->status,
                    'preview' => $item->preview,
                    'free' => $item->free,
                ], auth()->id());

                if ($item->contentMedia) {
                    $media = $mediaRepository->create([
                        'name' => $item->contentMedia->name,
                        'thumbnail' => $item->contentMedia->thumbnail,
                        'duration' => $item->contentMedia->duration,
                        'size' => $item->contentMedia->size,
                        'url' => $item->contentMedia->url,
                        'raw_url' => $item->contentMedia->raw_url,
                        'type' => $item->contentMedia->type,
                        'owner' => $item->contentMedia->owner,
                        'curriculum_item' => $newSection->id,
                        'status' => $item->contentMedia->status,
                    ]);

                    $newSection['content'] = $media;
                }

                return $newSection;
            }
        }
    }

    public function moveItemToParent(Request $request)
    {
        if (!Auth::user()->can('curriculum_edit')) {
            abort(403);
        }

        try {
            $inputs = $request->all('id', 'userid');
            $courseCurriculumItem = CourseCurriculumItems::find($inputs['id']);

            if ($inputs['userid'] && $courseCurriculumItem && $courseCurriculumItem->parent_section != 0) {
                $course = $courseCurriculumItem->getCourse;
                $name = $courseCurriculumItem->name;
                $slug = convert_to_slug($name);

                if (Course::whereSlug($slug)->exists()) {
                    $slug = convert_to_slug($name, true);
                }

                $courseData = [
                    'name' => $name,
                    'slug' => $slug,
                    'editor' => $inputs['userid'],
                    'author' => $inputs['userid'],
                    'type' => $courseCurriculumItem->type,
                    'price' => 0,
                    'classlevel' => $course->classlevel,
                    'subject' => $course->subject,
                    'season_id' => $course->season_id,
                    'level' => $course->level,
                ];

                DB::transaction(function () use ($courseData, &$courseCurriculumItem) {
                    $newCourse = Course::create($courseData);
                    $courseCurriculumParent = $courseCurriculumItem->getParentCurriculum;

                    if ($courseCurriculumParent) {
                        $newCurriculumParent = $courseCurriculumParent->replicate();
                        $newCurriculumParent->course_id = $newCourse->id;
                        $newCurriculumParent->save();
                    }

                    $courseCurriculumItem->update([
                        'course_id' => $newCourse->id,
                        'parent_section' => $newCurriculumParent->id ?? 0,
                    ]);
                });

                return [
                    'code_status' => true
                ];
            }
        } catch (\Exception $e) {
            return [
                'code_status' => false
            ];
        }
    }

    /**
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function onSortEnd(Request $request)
    {
        if (!Auth::user()->can('curriculum_edit')) {
            abort(403);
        }

        $data = $request->all();
        $result = $this->repository->onSortEnd($data['items'], $data['oldIndex'], $data['newIndex']);
        return $result;
    }

    public function updateOnCopy(Request $request)
    {
        if (!Auth::user()->can('curriculum_edit')) {
            abort(403);
        }

        $course = CourseCurriculumItems::find($request->id)->getCourse;
        $items = CourseCurriculumItems::where('course_id', $course->id)->orderBy('index')->get();
        $index = 0;
        foreach ($items as $item) {
            if ($item->id == $request->id) {
                $temp = $item->toArray();
                unset($temp['id']);
                $temp['index'] = $index++;
                CourseCurriculumItems::create($temp);
            }
            $item->index = $index;
            $item->save();
            $index++;
        }
        $section = $this->repository->getAllSection($course->id, $request);

        return $section;
    }

    /**
     * @param \Illuminate\Http\Request $request
     *
     * @return mixed
     */
    public function updateDescription(Request $request)
    {
        $data = $request->all();
        $item = $this->repository->updateDescription($data);
        return $item;
    }

    /**
     * @param \Illuminate\Http\Request $request
     *
     * @return mixed
     */
    public function updateStatus(Request $request)
    {
        $data = $request->all();
        $item = $this->repository->updateStatus($data);
        return $item;
    }

    /**
     * @param \Illuminate\Http\Request $request
     *
     * @return mixed
     */
    public function updatePreview(Request $request)
    {
        $data = $request->all();
        $item = $this->repository->updatePreview($data);
        return $item;
    }

    /**
     * @param $courseId
     *
     * @return mixed
     */
    public function getLectures(Request $request)
    {
        $courseId = $request->get('course_id');
        $item = $this->repository->orderBy('created_at', 'desc')
            ->findWhere([
                'course_id' => $courseId,
                'status' => 'active',
                'type' => 'lecture'
            ], ['name', 'id'])->all();
        return $item;
    }

    public function getRelated(Request $request)
    {
        $courseId = $request->get('course_id');

        $course = $courseId ? Course::find($courseId) : null;

        if (!$course) return [];

        return $course->relatedLectures;
    }

    public function searchRelated(Request $request)
    {
        if (!Auth::user()->can('assign_video')) {
            abort(403);
        }

        $page = $request->input('page');
        $page = intval($page) ?: 1;
        $limit = 20;
        $searchText = $request->get('searchText');

        $query = CourseCurriculumItems::where('type', 'lecture')
            ->offset(($page - 1) * $limit)
            ->limit($limit);

        if ($searchText) {
            if (is_numeric($searchText)) {
                $query->where('id', $searchText);
            } else {
                $query->where('name', 'like', '%' . $searchText . '%');
            }
        }

        $items = $query->get();

        return response()->json([
            'items' => $items,
            'page' => $page,
            'hasMore' => count($items) >= $limit,
        ]);
    }

    public function addRelated(Request $request)
    {
        if (!Auth::user()->can('assign_video')) {
            abort(403);
        }

        $courseId = $request->get('course_id');
        $relatedId = $request->get('related_id');

        $course = $courseId ? Course::find($courseId) : null;
        $related = null;

        if ($course && $relatedId) {
            $related = CourseCurriculumItems::find($relatedId);

            if ($related) {
                $check = LectureTest::where([
                    'type' => LectureTest::TYPE_TEST_LECTURE,
                    'lecture_id' => $related->id,
                    'test_id' => $course->id,
                ])->first();

                if (!$check) {
                    LectureTest::create([
                        'type' => LectureTest::TYPE_TEST_LECTURE,
                        'lecture_id' => $related->id,
                        'test_id' => $course->id,
                    ]);

                    $course->increment('related_lectures_count', 1);
                }
            }
        }

        return response()->json([
            'related' => $related,
        ]);
    }

    public function deleteRelated(Request $request)
    {
        if (!Auth::user()->can('assign_video')) {
            abort(403);
        }

        $courseId = $request->get('course_id');
        $relatedId = $request->get('related_id');

        $course = $courseId ? Course::find($courseId) : null;
        $deleted = false;

        if ($course && $relatedId) {
            $deleted = LectureTest::where([
                'type' => LectureTest::TYPE_TEST_LECTURE,
                'lecture_id' => $relatedId,
                'test_id' => $course->id,
            ])->delete();

            if ($deleted && $course->related_lectures_count > 0) {
                $course->decrement('related_lectures_count', 1);
            }
        }

        return response()->json([
            'deleted' => $deleted,
        ]);
    }

    /**
     * @param Request $request
     * @return \Illuminate\Config\Repository|mixed
     */
    public function uploadImage(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:jpg,png,jpeg|max:10000',
        ]);

        $image = $request->file('file');
        $location = $this->path . config('app.key_save_video') . '/quiz_source1/' . date('Y') . '/' . date('m');

        if (!File::exists($location)) {
            File::makeDirectory($location, 0775, true, true);
        }

        $name = $image->getClientOriginalName();
        $fileName = str_slug(pathinfo($name)['filename'], '-');
        $extension = File::extension($name);
        $fullName = $fileName . '-' . time() . '.' . $extension;

        if (File::exists($location . '/' . $fullName)) {
            $fullName = $fileName . '-1' . '.' . $extension;
        }

        $image->move($location, $fullName);

        $url = $this->baseUrl . config('app.key_save_video') . '/quiz_source1/' . date('Y') . '/' . date('m') . '/' . $fullName;

        return $url;
    }

    public function changeFree(Request $request)
    {
        if ($request->id){
            CourseCurriculumItems::where('id', $request->id)->update(['free' => $request->ischecked]);
        }

        return response()->json(['message' => 'Cập nhật thành công']);
    }

    public function uploadMedia(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:jpg,png,jpeg,mp4,mov,mp3,wav,bin|max:4166656',
        ]);

        $media = $request->file('file');
        $location = $this->path . config('app.key_save_video') . '/quiz_source1/' . date('Y') . '/' . date('m');

        if (!File::exists($location)) {
            File::makeDirectory($location, 0775, true, true);
        }

        $name = $media->getClientOriginalName();
        $fileName = str_slug(pathinfo($name)['filename'], '-');
        $extension = File::extension($name);
        $fullName = $fileName . '-' . time() . '.' . $extension;

        if (File::exists($location . '/' . $fullName)) {
            $fullName = $fileName . '-1' . '.' . $extension;
        }

        $media->move($location, $fullName);

        $url = $this->baseUrl . config('app.key_save_video') . '/quiz_source1/' . date('Y') . '/' . date('m') . '/' . $fullName;

        return $url;
    }

    public function getTestForLecture(Request $request)
    {
        $lectureId = $request->get('id');
        $classlevel = $request->get('classlevel');
        $subject = $request->get('subject');
        $page = $request->input('page');
        $page = intval($page) ?: 1;
        $limit = 8;

        $searchText = $request->get('searchText');

        $query = Course::where('subject', $subject)
            // ->where('classlevel', $classlevel)
            ->where('type', 'test')
            ->where('status', 'active')
            ->offset(($page - 1) * $limit)
            ->limit($limit);

        if ($searchText) {
            $query->where('name', 'like', '%' . $searchText . '%');
        }

        $tests = $query->get();

        return response()->json([
            'tests' => $tests,
            'page' => $page,
            'hasMore' => count($tests) >= $limit,
        ]);
    }

    public function getTestOfLecture(Request $request)
    {
        $lectureId = $request->get('lecture_id');
        $tests = collect([]);
        $lecture = $lectureId ? CourseCurriculumItems::find($lectureId) : null;

        if ($lecture) {
            $tests = $lecture->tests;
        }

        return response()->json([
            'tests' => $tests,
        ]);
    }

    public function addTestForLecture(Request $request)
    {
        $lectureId = $request->get('lecture_id');
        $testId = $request->get('test_id');
        $lecture = CourseCurriculumItems::where('id', $lectureId)->first();

        $lecture->tests()->attach($testId);

        return response()->json([
            'tests' => $lecture->tests,
        ]);
    }

    public function deleteTestForLecture(Request $request)
    {
        $lectureId = $request->get('lecture_id');
        $testId = $request->get('test_id');
        $lecture = CourseCurriculumItems::where('id', $lectureId)->first();

        $lecture->tests()->detach($testId);

        return response()->json([
            'tests' => $lecture->tests,
        ]);
    }
}
