<?php

namespace Course\Repositories;

use Course\Models\Course;
use Course\Models\CourseCurriculumItems;
use Prettus\Repository\Eloquent\BaseRepository;
use Illuminate\Support\Facades\DB;

class CourseCurriculumItemsRepository extends BaseRepository
{
    public function model()
    {
        // TODO: Implement model() method.
        return CourseCurriculumItems::class;
    }

    /**
     * Get all section with course_id
     *
     * @param $id
     *
     * @return mixed
     */
    public function getAllSection($id, $request)
    {
        return CourseCurriculumItems::where('course_id', $id)
            ->with([
                'getCourse',
                'contentMedia',
                'resourceMedia'
            ])
            ->where('type', 'lecture')
            ->get()
            ->concat(
                CourseCurriculumItems::where('course_id', $id)
                    ->with([
                        'getCourse',
                        'contentMedia',
                        'getAllQuestion' => function ($query) {
                            $query->with('getOwner', 'getAnswer');
                        }
                    ])
                    ->whereIn('type', ['test', 'essay', 'section'])
                    ->get()
            )->sortBy('index');
    }

    /**
     * Add new section to db
     * @param $data
     *
     * @return array|mixed
     */
    public function addSection($data, $userid)
    {
        DB::beginTransaction();

        try {
            $qty = $this->findWhere([
                'course_id' => $data['course_id']
            ])->count();

            $data['index'] = $qty;

            if (!empty($data['quiz_content'])) {
                $data['quiz_content'] = is_array($data['quiz_content']) ?
                    $data['quiz_content'] :
                    json_decode($data['quiz_content'], true);
            }

            $parentSectionId = 0;

            if ($data['type'] != 'section') {
                $parentSection = $this->orderBy('index', 'desc')
                    ->findWhere([
                        'type' => 'section',
                        ['index', '<', $qty],
                        'course_id' => $data['course_id']
                    ])->first();

                $parentSectionId = (!empty($parentSection)) ? $parentSection->id : 0;
                $data['parent_section'] = $parentSectionId;

                Course::where('id', $data['course_id'])->increment('curriculums_count', 1);
            }

            $newSection = $this->create($data);

            $newSection['status'] = 'disable';
            $newSection['onEdit'] = false;
            $newSection['content'] = [];
            $newSection['resource'] = [];
            $newSection['displayLectureAddForm'] = false;
            $newSection['onEditQuestion'] = false;
            $newSection['displayQuizForm'] = false;
            $newSection['displayTestForm'] = false;
            $newSection['show'] = false;
            $newSection['showContent'] = false;
            $newSection['showEditor'] = false;
            $newSection['showVideoContent'] = false;
            $newSection['showYoutubeContent'] = false;
            $newSection['showResourceContent'] = false;

            DB::commit();

            return $newSection;
        } catch (\Exception $e) {
            DB::rollBack();

            return $e->getMessage();
        }
    }

    /**
     * @param $data
     *
     * @return mixed
     */
    public function updateItem($data)
    {
        if (!empty($data['quiz_content'])) {
            $data['quiz_content'] = is_array($data['quiz_content']) ?
                $data['quiz_content'] :
                json_decode($data['quiz_content'], true);
        }

        $newItem = $this->update($data, $data['id']);

        return $newItem;
    }

    /**
     * @param $data
     */
    public function onSortEnd($data, $oldIndex, $newIndex)
    {
        $theLastSection = null;
        foreach ($data as $k => $value) {
            $update = ['index' => $k];
            if ($theLastSection && $value['type'] != 'section') {
                $update['parent_section'] = $theLastSection['id'];
                if (isset($value['status']) && $value['status'] == 'active') {
                    $check_status++;
                }
            } else {
                $update['parent_section'] = 0;
            }
            $this->update($update, $value['id']);
            if ($value['type'] == 'section') {
                if (isset($check_status)) {
                    if (!$check_status) {
                        $this->update(['status' => 'disable'], $theLastSection['id']);
                    } else {
                        $this->update(['status' => 'active'], $theLastSection['id']);
                    }
                }
                $theLastSection = $value;
                $check_status = 0;
            }
        }
        if (isset($check_status)) {
            if (!$check_status) {
                $this->update(['status' => 'disable'], $theLastSection['id']);
            } else {
                $this->update(['status' => 'active'], $theLastSection['id']);
            }
        }
    }

    /**
     * @param $data
     *
     * @return mixed
     */
    public function updateDescription($data)
    {
        $item = $this->update([
            'description' => $data['description']
        ], $data['value']['id']);
        return $item;
    }

    /**
     * @param $data
     *
     * @return mixed
     */
    public function updateStatus($data)
    {
        try {
            $status = $data['value']['status'];

            if ($status == 'disable') {
                $status = 'active';
            } else if ($status == 'active') {
                $status = 'disable';
            }

            //cap nhat trang thai curriculum truoc
            $item = $this->update([
                'status' => $status
            ], $data['value']['id']);

            $item['onEdit'] = false;
            $item['content'] = [];
            $item['displayLectureAddForm'] = false;
            $item['onEditQuestion'] = false;
            $item['displayQuizForm'] = false;
            $item['displayTestForm'] = false;
            $item['show'] = true;
            $item['showContent'] = false;
            $item['showEditor'] = false;
            $item['showVideoContent'] = false;
            $item['showYoutubeContent'] = false;
            $item['showResourceContent'] = false;
            $item['resource'] = [];

            //Cap nhat trang thai section
            $section = $this->orderBy('index', 'desc')
                ->findWhere([
                    'course_id' => $data['value']['course_id'],
                    'type' => 'section',
                    ['index', '<', $item->index]
                ])->first(); //Lay ra section chan dau

            if (empty($section)) {
                return [
                    'section' => [
                        'id' => 0,
                        'status' => false
                    ]
                ];
            }

            $nextSection = $this->orderBy('index', 'desc')
                ->findWhere([
                    'course_id' => $data['value']['course_id'],
                    'type' => 'section',
                    ['index', '>', $section->index]
                ])->first(); //Lay ra section chan = [];
            if (!empty($nextSection)) { // Truong hop co nhieu hon 1 lecture
                $otherLecture = $this->findWhere([
                    'course_id' => $data['value']['course_id'],
                    ['type', '!=', 'section'],
                    ['index', '>', $section->index],
                    ['index', '<', $nextSection->index]
                ])->all(); // Cac lecture o giua
            } else {
                $otherLecture = $this->findWhere([
                    'course_id' => $data['value']['course_id'],
                    ['type', '!=', 'section'],
                    ['index', '>', $section->index]
                ])->all();
            }

            //kiem tra lecture duoc active
            $sectionStatus = 'disable';
            foreach ($otherLecture as $l) {
                if ($l->status == 'active') {
                    $sectionStatus = 'active';
                    break;
                }
            }

            $newSection = $this->update([
                'status' => $sectionStatus
            ], $section->id);

            return [
                'section' => [
                    'id' => $newSection->id,
                    'status' => $sectionStatus
                ]
            ];

        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    public function updatePreview($data)
    {
        $preview = $data['value']['preview'];
        if ($preview == 'disable') {
            $preview = 'active';
        } else if ($preview == 'active') {
            $preview = 'disable';
        }

        $item = $this->update([
            'preview' => $preview
        ], $data['value']['id']);

        $item['onEdit'] = false;
        $item['content'] = [];
        $item['displayLectureAddForm'] = false;
        $item['onEditQuestion'] = false;
        $item['show'] = true;
        $item['showContent'] = false;
        $item['showEditor'] = false;
        $item['showVideoContent'] = false;
        $item['showYoutubeContent'] = false;
        $item['displayQuizForm'] = false;
        $item['displayTestForm'] = false;
        $item['showResourceContent'] = false;
        $item['resource'] = [];
        return $item;
    }

    public function createSection($courseId, $name)
    {
        return $this->create([
            'course_id' => $courseId,
            'name' => $name,
            'index' => 0,
            'type' => 'section',
        ]);
    }
}
