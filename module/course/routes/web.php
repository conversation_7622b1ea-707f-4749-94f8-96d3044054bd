<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Routing\Router;
use Course\Models\CourseCurriculumItems;

$adminRoute = config('base.admin_route');
$moduleRoute = 'course';

Route::group(['prefix' => $adminRoute, 'middleware' => 'check-role-manage'], function (Router $router) use ($adminRoute, $moduleRoute) {
    $router->group(['prefix' => $moduleRoute], function (Router $router) use ($adminRoute, $moduleRoute) {
        $router->get('index', 'CourseController@getIndex')
            ->name('nqadmin::course.index.get')
            ->middleware('permission:course_index|course_index_full');

        $router->get('create', 'CourseController@getCreate')
            ->name('nqadmin::course.create.get')
            ->middleware('permission:course_create|course_index_full');

        $router->post('create', 'CourseController@postCreate')
            ->name('nqadmin::course.create.post')
            ->middleware('permission:course_create|course_index_full');

        $router->get('delete/{id}', 'CourseController@getDelete')
            ->name('nqadmin::course.delete.get')
            ->middleware('permission:course_delete|course_index_full');

        $router->post('{course}/trending', 'CourseController@setTrending')
            ->name('nqadmin::course.trending')
            ->middleware('permission:course_create|course_index_full');

        $router->post('{course}/trending-index', 'CourseController@setTrendingIndex')
            ->name('nqadmin::course.trending_index')
            ->middleware('permission:course_create|course_index_full');

        $router->post('{course}/change-pattern', 'CourseController@changePattern')
            ->name('nqadmin::course.pattern.change')
            ->middleware('permission:course_create|course_index_full');
        
        $router->post('{course}/change-free', 'CourseController@changeFree')
            ->name('nqadmin::course.change.free')
            ->middleware('permission:course_create|course_index_full');

        $router->get('question', 'CourseController@getQuestion')
            ->name('nqadmin::course.question.get')
            ->middleware('permission:course_comment|course_index_full');

        $router->get('question-answer/{id}', 'CourseController@getQuestionAnswer')
            ->name('nqadmin::course.question-answer.get')
            ->middleware('permission:course_comment|course_index_full');

        $router->post('question-answer/{id}', 'CourseController@postAnswer')
            ->name('nqadmin::course.question-answer.post')
            ->middleware('permission:course_comment|course_index_full');

        $router->get('question-delete/{id}', 'CourseController@getQuestionDelete')
            ->name('nqadmin::course.question-delete.get')
            ->middleware('permission:course_index_full|course_comment');

        $router->get('answer-delete/{id}', 'CourseController@getAnswerDelete')
            ->name('nqadmin::course.answer-delete.get')
            ->middleware('permission:course_index_full|course_comment');

        $router->group(['middleware' => 'permission:course_create|course_index_full'], function (Router $router) use ($adminRoute, $moduleRoute) {
            //Course Landing Page
            $router->get('{id}/course-landingpage', 'CourseLandingPageController@getIndex')
                ->name('nqadmin::course.landingpage.get');

            $router->get('remove-promo/{id}', 'CourseLandingPageController@deletePromovideo')
                ->name('nqadmin::course.removepromovideo');

            $router->post('{id}/course-landingpage', 'CourseLandingPageController@postIndex')
                ->name('nqadmin::course.landingpage.post');

            //Course Targe
            $router->get('{id}/create-course-target', 'CourseTargetController@getIndex')
                ->name('nqadmin::course.target.get');

            $router->post('{id}/create-course-target', 'CourseTargetController@postIndex')
                ->name('nqadmin::course.target.post');

            //autocomplete tag
            $router->get('get-tag-auto', 'CourseLandingPageController@getTagAuto')
                ->name('nqadmin::course.tags.get');
        });
        $router->group(['middleware' => 'permission:course_create|course_index_full|course_upload'], function (Router $router) use ($adminRoute, $moduleRoute) {
            //Course Curriculum
            $router->get('{id}/create-course-curriculum', 'CourseCurriculumController@getIndex')
                ->name('nqadmin::course.curriculum.get');
            $router->post('{id}/create-course-curriculum', 'CourseCurriculumController@postIndex')
                ->name('nqadmin::course.curriculum.post');
        });
        //Course Price
        $router->get('{id}/create-course-price', 'CoursePriceController@getIndex')
            ->name('nqadmin::course.price.get')
            ->middleware('permission:course_index_full');

        //Course save Price
        $router->post('{id}/create-course-price', 'CoursePriceController@save')
            ->name('nqadmin::course.price.save')
            ->middleware('permission:course_index_full');

        //enable course
        $router->get('enable/{id}', 'CourseController@getEnable')
            ->name('nqadmin::course.enable.get')
            ->middleware('permission:course_index_full');

        $router->post('change-assistant/{id}', 'CourseController@changeAssistant')
            ->name('nqadmin::course.changeassistant.post')
            ->middleware('permission:course_index_full');

        //enable course post
        $router->get('enable-post/{id}', 'CourseController@postEnable')
            ->name('nqadmin::course.enable.post')
            ->middleware('permission:course_index_full');

        $router->get('enable', 'CourseController@getEnableList')
            ->name('nqadmin::course.enable.list')
            ->middleware('permission:course_index_full');

        $router->get('google-analytics-code/{id}', 'CourseController@googleAnalytics')
            ->name('nqadmin::course.google')
            ->middleware('permission:course_index_full');

        $router->post('google-analytics-code/{id}', 'CourseController@postgoogleAnalytics')
            ->name('nqadmin::course.google.post')
            ->middleware('permission:course_index_full');

        // enable all
        $router->get('enable-all', 'CourseController@enableAll')
            ->name('nqadmin::course.enable.all')
            ->middleware('permission:course_index_full');

        $router->get('export/{id}', 'CourseController@exportExcel')
            ->name('nqadmin::course.export.get');

        // upload video follow lecture of course
        $router->get('lecture/video/edit', 'CourseCurriculumController@editVideoForLecture')
            ->name('nqadmin::lecture.video.edit')
            ->middleware('permission:course_upload');
    });

    // ajax call
    $router->group(['prefix' => 'services'], function (Router $router) use ($moduleRoute) {
        $router->group(['prefix' => $moduleRoute], function (Router $router) {
            $router->group(['prefix' => 'curriculum'], function (Router $router) {
                $router->get('get-all-items', 'CourseCurriculumItemsController@getAllItems');
                $router->post('add-item', 'CourseCurriculumItemsController@addItem');
                $router->post('delete-item', 'CourseCurriculumItemsController@deleteItem');
                $router->post('update-item', 'CourseCurriculumItemsController@updateItem');
                $router->post('clone-item', 'CourseCurriculumItemsController@cloneItem');
                $router->post('move-item-to-parent', 'CourseCurriculumItemsController@moveItemToParent');
                $router->post('update-on-sort-end', 'CourseCurriculumItemsController@onSortEnd');
                $router->post('update-on-copy', 'CourseCurriculumItemsController@updateOnCopy');
                $router->post('update-description', 'CourseCurriculumItemsController@updateDescription');
                $router->post('update-course-status', 'CourseCurriculumItemsController@updateStatus');
                $router->post('update-preview-status', 'CourseCurriculumItemsController@updatePreview');
                $router->get('get-lectures', 'CourseCurriculumItemsController@getLectures');
                $router->get('get-test-for-lecture', 'CourseCurriculumItemsController@getTestForLecture');
                $router->get('get-test-of-lecture', 'CourseCurriculumItemsController@getTestOfLecture');
                $router->post('add-test-for-lecture', 'CourseCurriculumItemsController@addTestForLecture');
                $router->post('delete-test-for-lecture', 'CourseCurriculumItemsController@deleteTestForLecture');
                $router->get('get-related', 'CourseCurriculumItemsController@getRelated');
                $router->get('search-related', 'CourseCurriculumItemsController@searchRelated');
                $router->post('add-related', 'CourseCurriculumItemsController@addRelated');
                $router->post('delete-related', 'CourseCurriculumItemsController@deleteRelated');
            });
        });
    });
});

// Frontend
Route::group(['namespace' => 'Frontend'], function (Router $router) {
    // normal
    $router->get('/{slug}/preview', 'CourseBuyController@previewCourse')
        ->name('front.course.preview.get');

    $router->get('/{slug}', 'CourseBuyController@getCourse')
        ->name('front.course.get');

    $router->get('/{slug}/hoc/bai-hoc/{lectureId?}', 'LectureController@getIndex')
        ->name('nqadmin::course.lecture.learn')
        ->middleware('auth');

    $router->get('/{slug}/preview/video/{lectureId?}', 'LecturePreviewController@getPreviewNormalCourse')
        ->name('front.video.preview.get');

    $router->post('/video/process/{courseId}/{lectureId}', 'LectureController@updateProcessVideo')
        ->name('video.process')
        ->middleware('auth');

    // lecture
    $router->group(['prefix' => 'hoc-online'], function () use ($router) {
        $router->get('{slug}/{lectureId?}', 'LectureController@previewLecture')
            ->name('front.lecture.get');
    });

    // exam
    $router->group(['prefix' => 'thi-online'], function () use ($router) {
        $router->get('{slug}/{lectureId?}', 'LectureController@previewExam')
            ->name('front.exam.get');
        // Tự luận - link cũ
        $router->get('{slug}/{lectureId}/tu-luan', function($slug, $lectureId) {
            return redirect()->route('front.exam.get', [$slug, $lectureId]);
        })->name('front.course.essay.show');
        $router->get('/{slug}/{lectureId}/thi', 'LectureController@startMultichoice')
            ->name('nqadmin::course.multi-choice.start')
            ->middleware('auth');
    });
    $router->group(['prefix' => 'ket-qua-thi'], function () use ($router) {
        $router->get('{testResult}', 'LectureController@resultMultichoice')
            ->name('nqadmin::course.multi-choice.result')
            ->middleware('auth');
    });
    $router->group(['prefix' => 'exam'], function () use ($router) {
        $router->post('{exam}/play', 'LectureController@playExam')
            ->middleware('auth');
        $router->post('{testResult}/submit', 'LectureController@submitExam')
            ->middleware('auth');
        $router->get('{id}/download', 'LectureController@downloadExam')
            ->name('front.exam.download')
            ->middleware('auth');
    });

    // Câu hỏi
    $router->get('question/{id}/{slug?}', 'LecturePreviewController@previewQuestion')
        ->name('front.question.preview.get');

    // rating
    $router->post('khoa-hoc/rating', 'CourseBuyController@postRating')
        ->name('front.course.rating.post')
        ->middleware('auth');
});

// api video
Route::get('media/video-url/{lectureId}/{type}/{quality?}', function($lectureId, $type, $quality = null) {
    $lecture = CourseCurriculumItems::find($lectureId);
    $media = $lecture->getMedia->where('type', 'video/mp4')->first();
    $url = null;

    if ($media) {
        if ($quality) {
            $videoUrlQuality = rtrim($media->raw_url, '.mp4') . '_x' . $quality . '.mp4';

            if (file_exists(public_path($videoUrlQuality))) {
                $url = config('app.url') . 'media/video/' . $media->encodeVideoUrl(getClipTo($lecture->preview, $type), $videoUrlQuality) . '/video.m3u8';
            }
        } else {
            if ($media->checking == 1) {
                $url = rtrim(config('app.url'), '/') . $media->url;
            } else if ($media->checking == 2 || $media->checking == 5) {
                $urlArr = explode('/', $media->raw_url);
                $videoUrlBackup = '/backup/old_video/'. end($urlArr);
                $url = config('app.url') . 'media/video/' . $media->encodeVideoUrl(getClipTo($lecture->preview, $type), $videoUrlBackup) . '/video.m3u8';
            } else {
                $url = config('app.url') . 'media/video/' . $media->encodeVideoUrl(getClipTo($lecture->preview, $type)) . '/video.m3u8';
            }
        }
    }

    return response()->json([ 'url' => $url ]);
})->name('videourl');

Route::get('media/video/{token}/video.m3u8', function($token = null) {
    try {
        $decoded = \Firebase\JWT\JWT::decode($token, config('app.video_url_secret'), array('HS256'));
        $videoUrl = config('app.url') . $decoded->raw_url . ($decoded->clipTo ? '/clipTo/' . $decoded->clipTo : '') . '/index.m3u8';
        $client = new \GuzzleHttp\Client();
        $res = $client->request('GET', $videoUrl, ['http_errors' => false]);

        if ($res->getStatusCode() == 200) {
            return response($res->getBody()->getContents(), 200)->header('Content-Type', 'application/vnd.apple.mpeg');
        }

        throw new \Exception('Not get file: ' . $videoUrl);
   } catch (\Exception $e) {
       Log::error($e);

       return abort(500);
   }
})->name('mediavideo');
// end api video
