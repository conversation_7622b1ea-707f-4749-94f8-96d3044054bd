<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Routing\Router;

$moduleRoute = 'course';

Route::group(['prefix' => $moduleRoute], function (Router $router) use ($moduleRoute) {
    $router->group(['prefix' => 'curriculum'], function (Router $router) {
        $router->post('upload-image', 'CourseCurriculumItemsController@uploadImage');
        $router->post('upload-media', 'CourseCurriculumItemsController@uploadMedia');
        $router->post('change-free', 'CourseCurriculumItemsController@changeFree');
    });
});
