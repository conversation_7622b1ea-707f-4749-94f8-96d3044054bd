<?php

namespace Media\Http\Controllers;

use Barryvdh\Debugbar\Controllers\BaseController;
use Illuminate\Http\Request;
use Media\Http\Requests\VideoRequest;
use Media\Repositories\MediaRepository;
use Course\Repositories\CourseCurriculumItemsRepository;
use Media\Models\Media;
use App\Jobs\ConvertVideoToSD;

class MediaController extends BaseController
{
    protected $mediaRepository;
    protected $courseCurriculumItemsRepository;

    public function __construct(
        MediaRepository $mediaRepository,
        CourseCurriculumItemsRepository $courseCurriculumItemsRepository
    )
    {
        $this->mediaRepository = $mediaRepository;
        $this->courseCurriculumItemsRepository = $courseCurriculumItemsRepository;
    }

    /**
     * @param \Illuminate\Http\Request $request
     *
     * @return array|mixed
     */
    public function upload(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:mp4,mov|max:4166656', // max: 4GB
        ]);

        $userid = $request->get('userid');
        $curriculum = $request->get('curriculum');
        // Change status of curriculum with have been new uploaded
        $cur = $this->courseCurriculumItemsRepository->update([
            'status' => 'disable',
            'preview' => 'disable',
        ], $curriculum);
        $file = $request->file('file');

        $this->mediaRepository->removeOldContent($curriculum);

        $result = $this->mediaRepository->upload($userid, $file, $curriculum, $type = 'video');

        $cur->getParentCurriculum->update(['status' => 'active']);

        return response()->json([
            'video' => $result,
            'curriculum' => $cur
        ]);
    }

    public function uploadVideoSD(VideoRequest $request)
    {
        $result = $this->mediaRepository->uploadVideoSD($request->media_id, $request->file('file'));

        return response()->json($result);
    }

    public function uploadYoutube(Request $request)
    {
        $request->validate([
            'url' => 'required|url',
            'userid' => 'required',
            'curriculum' => 'required',
        ]);

        $url = $request->get('url');
        $curriculum = $request->get('curriculum');

        $this->mediaRepository->removeOldContent($curriculum, 'youtube');

        $media = $this->mediaRepository->create([
            'name' => getIdVideoYoutube($url), // is ID youtube
            'url' => $url,
            'raw_url' => $url, 
            'type' => 'youtube',
            'owner' => $request->get('userid'),
            'curriculum_item' => $curriculum,
            'status' => 'active',
        ]);
        $cur = $this->courseCurriculumItemsRepository->find($curriculum);
        $cur->update(['status' => 'active']);

        if ($cur->getParentCurriculum) {
            $cur->getParentCurriculum->update(['status' => 'active']);
        }

        return response()->json([
            'youtube' => $media,
            'curriculum' => $cur
        ]);
    }

    public function download(Request $request)
    {
        $url = $request->url;

        return $this->mediaRepository->download($url);
    }

    /**
     * Api for media lib get list video unused
     * @param \Illuminate\Http\Request $request
     *
     * @return mixed
     */
    public function getListVideo(Request $request)
    {
        $owner = $request->get('owner');
        $media = $this->mediaRepository->getListVideo($owner);

        return $media;
    }

    /**
     * Api for media lib delete video
     * @param \Illuminate\Http\Request $request
     *
     * @return string
     */
    public function delete(Request $request)
    {
        $id = $request->get('id');

        try {
            $this->mediaRepository->deleteMedia($id);
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    /**
     * Api for media lib, change curriculum item content
     * @param \Illuminate\Http\Request $request
     *
     * @return mixed
     */
    public function updateCuriculumVideo(Request $request)
    {
        try {
            $id = $request->get('id');
            $curitem = $request->curItem['id'];
            $owner = $request->owner;

            $oldMedia = $this->mediaRepository->findWhere([
                'curriculum_item' => $curitem,
                'type' => 'video/mp4',
            ])->first();

            if (!empty($oldMedia)) {
                $this->mediaRepository->update([ //set null to old media attached to this curriculum
                    'curriculum_item' => null
                ], $oldMedia->id);
            }

            $this->mediaRepository->update([ //update new media to this curriculum
                'curriculum_item' => $curitem
            ], $id);

            $curriculum = $this->courseCurriculumItemsRepository->update([
                'preview' => 'disable'
            ], $curitem);
            //return new media list
            $media = $this->mediaRepository->getListVideo($owner);

            return [
                'media' => $media,
                'curriculum' => $curriculum,
            ];
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    /**
     * @param \Illuminate\Http\Request $request
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadResource(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:pdf,doc,docx,xls,xlsx,ppt,pptx|max:4166656', // max: 4GB
        ]);

        $userid = $request->get('userid');
        $curriculum = $request->get('curriculum');

        // Change status of curriculum with have been new uploaded
        $c = $this->courseCurriculumItemsRepository->find($curriculum);
        $cur = $this->courseCurriculumItemsRepository->update(['status' => 'active'], $c->id);

        $cur->getParentCurriculum->update(['status' => 'active']);

        $file = $request->file('file');
        $result = $this->mediaRepository->upload($userid, $file, $curriculum, $type = 'resources');

        return response()->json([
            'resource' => $result,
            'curriculum' => $cur,
            'curriculumSection' => $cur->getParentCurriculum,
        ]);
    }

    /**
     * @param \Illuminate\Http\Request $request
     *
     * @return string
     */
    public function deleteResource(Request $request)
    {
        $id = $request->get('id');

        try {
            $this->mediaRepository->deleteResource($id);
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    public function setNullResource(Request $request)
    {
        $id = $request->get('id');

        try {
            $this->mediaRepository->setNullResource($id);
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    /**
     * @param \Illuminate\Http\Request $request
     *
     * @return mixed
     */
    public function getListResource(Request $request)
    {
        $owner = $request->get('owner');
        $media = $this->mediaRepository->getListResource($owner);
        return $media;
    }

    /**
     * @param \Illuminate\Http\Request $request
     *
     * @return mixed
     */
    public function updateResource(Request $request)
    {
        $id = $request->get('id');
        $curitem = $request->curItem['id'];
        $owner = $request->owner;

        $this->mediaRepository->update([
            'curriculum_item' => $curitem
        ], $id);

        $media = $this->mediaRepository->getListResource($owner);
        $curriculum = $this->courseCurriculumItemsRepository->update([
            'preview' => 'disable'
        ], $curitem);

        return [
            'media' => $media,
            'curriculum' => $curriculum
        ];
    }

    public function liveStream(Request $request)
    {
        $userid = $request->get('userid');
        $curriculum = $request->get('curriculum');

        //Change status of curriculum with have been new uploaded
        $c = $this->courseCurriculumItemsRepository->find($curriculum);
        $cur = $this->courseCurriculumItemsRepository->update(['status' => 'disable'], $c->id);
        $url = $request->urlstream;

        $this->mediaRepository->removeOldContent($curriculum);

        $result = $this->mediaRepository->connect($userid, $url, $curriculum);

        return response()->json([
            'video' => $result,
            'curriculum' => $cur
        ]);
    }

    public function convertVideoToSD(Request $request)
    {
        $videoMedia = Media::where('id', $request->media_id)
            ->where('type', 'video/mp4')
            ->whereNotNull('curriculum_item')
            ->first();

        if ($videoMedia) {
            if ($videoMedia->checking == 5 || $videoMedia->checking == 6) { // in process convert sd
                return response()->json([
                    'convert' => 'refuse',
                    'message' => 'Video đang convert, vui lòng thử lại sau!',
                ]);
            }

            $videoMedia->update([
                'checking' => $videoMedia->checking == 2 ? 5 : 6,
            ]);

            ConvertVideoToSD::dispatch($videoMedia)->onQueue('convert_to_sd');

            return response()->json([
                'convert' => 'processing',
            ]);
        }

        return response()->json([
            'convert' => 'failed',
            'message' => 'Media not exist!'
        ]);
    }
}
