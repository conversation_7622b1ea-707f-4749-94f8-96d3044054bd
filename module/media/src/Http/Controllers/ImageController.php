<?php

namespace Media\Http\Controllers;

use Barryvdh\Debugbar\Controllers\BaseController;
use Base\Services\Image\ImageService;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;
use Cart\Models\Order;
use Users\Models\OrderInvoice;
use Illuminate\Support\Facades\Auth;

class ImageController extends BaseController
{
    protected $imageService;

    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    private function validationRule()
    {
        return [
            'image' => ['required', 'mimes:' . config('image.types'), 'max:' . config('image.max_size')],
        ];
    }

    public function uploadInvoices(Request $request)
    {
        $inputs = [];
        $uploaded = false;

        try {
            $inputs = $request->validate($this->validationRule());
        } catch (ValidationException $e) {
            return response()->json([
                'uploaded' => $uploaded,
                'error' => [
                    'message' => $e->errors()['image'][0],
                ],
            ]);
        }

        $prefixFolder = 'invoices';
        $uploadedPath = $this->imageService->upload($prefixFolder, $inputs['image']);

        $order = Order::find($request->id);

        if ($order) {
            $orderInvoices = OrderInvoice::firstOrNew(['order_id' => $order->id]);
            $orderInvoices->order_id = $order->id;
            $orderInvoices->date = date('d/m/Y');
            $orderInvoices->money = $order->total_price;
            $orderInvoices->content = __(':name upload biên lai!', ['name' => Auth::user()->email]);

            if ($orderInvoices->image) {
                $this->imageService->delete($orderInvoices->image);
            }

            $orderInvoices->image = $uploadedPath;

            $orderInvoices->save();

            $uploaded = true;
        }

        return response()->json([
            'uploaded' => $uploaded,
            'path' => $uploadedPath,
            'url' => asset($uploadedPath),
        ]);
    }
}
