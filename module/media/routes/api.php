<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Routing\Router;

$moduleRoute = 'media';

Route::group(['prefix' => $moduleRoute], function (Router $router) use ($moduleRoute) {
    $router->group(['prefix' => 'video'], function (Router $router) {
        $router->post('upload', 'MediaController@upload');
        $router->post('upload-sd', 'MediaController@uploadVideoSD');
        $router->post('upload-youtube', 'MediaController@uploadYoutube');
        $router->get('download', 'MediaController@download');
        $router->post('live-stream', 'MediaController@liveStream');
        $router->post('delete-video', 'MediaController@delete');
    });

    $router->group(['prefix' => 'resource'], function (Router $router) {
        $router->post('upload-resource', 'MediaController@uploadResource');
    });
});
