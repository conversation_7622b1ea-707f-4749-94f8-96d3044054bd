<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Routing\Router;

$adminRoute = config('base.admin_route');

Route::group(['prefix' => $adminRoute, 'middleware' => 'check-role-manage'], function (Router $router) {
    // ajax call
    $router->group(['prefix' => 'services'], function (Router $router) {
        $router->group(['prefix' => 'media'], function (Router $router) {
            $router->group(['prefix' => 'video'], function (Router $router) {
                $router->get('get-list-video', 'MediaController@getListVideo');
                $router->post('update-curriculum-video', 'MediaController@updateCuriculumVideo');
                $router->post('convert-to-sd', 'MediaController@convertVideoToSD');
            });

            $router->group(['prefix' => 'resource'], function (Router $router) {
                $router->post('set-null-resource', 'MediaController@setNullResource');
                $router->get('get-list-resource', 'MediaController@getListResource');
                $router->post('update-resource', 'MediaController@updateResource');
                $router->post('delete-resource', 'MediaController@deleteResource');
            });
        });
    });
});

Route::group(['prefix' => 'upload', 'middleware' => 'auth'], function (Router $router) {
    $router->post('image-invoices', 'ImageController@uploadInvoices');
});
