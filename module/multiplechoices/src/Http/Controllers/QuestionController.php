<?php

namespace MultipleChoices\Http\Controllers;

use Barryvdh\Debugbar\Controllers\BaseController;
use Course\Repositories\CourseCurriculumItemsRepository;
use MultipleChoices\Repositories\AnswerRepository;
use MultipleChoices\Repositories\QuestionRepository;
use MultipleChoices\Models\Question;
use MultipleChoices\Models\Answer;
use MultipleChoices\Models\OldAnswer;
use MultipleChoices\Models\OldQuestion;
use MultipleChoices\Models\CurriculumQuestion;
use Course\Models\CourseCurriculumItems;
use MultipleChoices\Http\Resources\QuestionResource;
use MultipleChoices\Http\Requests\QuestionRequest;
use Illuminate\Http\Request;
use Base\Traits\CallApiTrait;
use \Firebase\JWT\JWT;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Storage;
use File;
use Illuminate\Support\Facades\Auth;

class QuestionController extends BaseController
{
    use CallApiTrait;

    protected $questionRepository;

    public function __construct(QuestionRepository $questionRepository)
    {
        $this->questionRepository = $questionRepository;
    }

    public function getQuestion($id)
    {
        // return Question::withCount('oldQuestions')
        //     ->with('getLecture', 'getAnswer')
        //     ->find($id);
    }

    public function getOldQuestion($question_id)
    {
        // return OldQuestion::where('question_id', $question_id)
        //     ->with('oldAnswers')
        //     ->orderBy('index', 'DESC')
        //     ->get();
    }

    public function restoreOldQuestion($question_id, $old_question_id)
    {
        // if (!Auth::user()->can('crud_all_question')) {
        //     abort(403);
        // }

        // $oldQuestion = OldQuestion::where('id', $old_question_id)
        //     ->where('question_id', $question_id)
        //     ->first();

        // if ($oldQuestion) {
        //     return $this->questionRepository->restoreQuestion($oldQuestion);
        // }

        // return response()->json([
        //     'error' => 'Không tìm thấy câu hỏi !!!',
        // ], 422);
    }

    public function addQuestion(QuestionRequest $request)
    {
        if (!Auth::user()->can('create_question')) {
            abort(403);
        }

        $data = $request->all();
        $question = $this->questionRepository->addQuestion($data);

        if (!$question) {
            return response()->json([
                'message' => 'Đã xảy ra lỗi! (Có thể bạn đang tạo quá 500 câu 1 đề)',
            ], 500);
        }

        return new QuestionResource($question->load('getAnswer'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     *
     * @return mixed
     */
    public function updateQuestion(QuestionRequest $request)
    {
        if (!Auth::user()->can('edit_question')) {
            abort(403);
        }

        $data = $request->all();
        $question = $this->questionRepository->updateQuestion($data);

        if (!$question) {
            return response()->json([
                'message' => 'Đã xảy ra lỗi! Vui lòng thử lại.',
            ], 500);
        }

        return new QuestionResource($question->load('getAnswer'));
    }

    public function updateQuestionById($id, QuestionRequest $request)
    {
        if (!Auth::user()->can('edit_question')) {
            abort(403);
        }

        $data = $request->all();
        $data['question_id'] = $id;
        $question = $this->questionRepository->updateQuestion($data);

        return new QuestionResource($question->load('getAnswer'));
    }

    /**
     * @param \Illuminate\Http\Request $request
     */
    public function deleteQuestion(Request $request)
    {
        if (!Auth::user()->can('delete_question')) {
            abort(403);
        }

        $curriculum = CourseCurriculumItems::with('getCourse')->find($request->curriculum_item);

        if (!$curriculum) {
            abort(404);
        }

        if (!empty($curriculum->getCourse) &&
            !Auth::user()->can('crud_all_question') &&
            $curriculum->getCourse->author != Auth::id()) {
            abort(403);
        }

        if (!empty($request->ids)) {
            DB::beginTransaction();

            try {
                foreach ($request->ids as $id) {
                    $question = Question::find($id);

                    if ($question) {
                        $countLectures = $question->getLectures()->count() ?: 0;

                        if ($countLectures < 2) {
                            // $question->forceDelete();
                            $question->delete();
                        }
                    }
                }

                CurriculumQuestion::where('curriculum_item', $request->curriculum_item)
                    ->whereIn('question_id', $request->ids)
                    ->delete();

                DB::commit();

                return response()->json([
                    'message' => 'Deleted',
                ]);
            } catch (\Exception $e) {
                DB::rollBack();

                return response()->json([
                    'error' => $e->getMessage(),
                ], 500);
            }
        }

        return response()->json([
            'error' => 'Thiếu dữ liệu.',
        ], 422);
    }

    /**
     * @param \Illuminate\Http\Request $request
     *
     * @return array
     */
    public function onSortEnd(Request $request)
    {
        if (!Auth::user()->can('edit_question')) {
            abort(403);
        }

        $data = $request->all();

        $this->questionRepository->onSortEnd($data['items']);

        return 'Sorted';
    }

    public function moveQuestion(Request $request)
    {
        if (!Auth::user()->can('edit_question')) {
            abort(403);
        }

        if ($request->curriculumId && $request->quesIds) {
            $curriculum = CourseCurriculumItems::find($request->curriculumId);

            if ($curriculum) {
                $oriCurriculum = null;

                if ($request->oriCurriculumId) {
                    $oriCurriculum = CourseCurriculumItems::with('getAllQuestion')->find($request->oriCurriculumId);
                }

                if ($oriCurriculum) {
                    if (!empty($curriculum->quiz_content)) {
                        return response()->json([
                            'moved' => false,
                            'message' => 'Mục mới có chưa đoạn văn, bạn cần phải đồng bộ đoạn văn trước để câu hỏi không bị sai đoạn văn!',
                        ]);
                    }

                    if (!empty($oriCurriculum->quiz_content)) {
                        $curriculum->update([
                            'quiz_content' => $oriCurriculum->quiz_content,
                        ]);
                    }

                    foreach ($oriCurriculum->getAllQuestion as $orQuestion) {
                        CurriculumQuestion::updateOrCreate(
                            [
                                'curriculum_item' => $curriculum->id,
                                'question_id' => $orQuestion->id,
                            ],
                            [
                                'status' => 1,
                                'index' => $orQuestion->pivot->index,
                            ]
                        );
                    }
                } else {
                    $indexInCurriculumQuestionTable = CurriculumQuestion::where('curriculum_item', $curriculum->id)
                        ->max('index') ?: 0;

                    foreach ($request->quesIds as $quesId) {
                        CurriculumQuestion::updateOrCreate(
                            [
                                'curriculum_item' => $curriculum->id,
                                'question_id' => $quesId,
                            ],
                            [
                                'status' => 1,
                                'index' => $indexInCurriculumQuestionTable + 1,
                            ]
                        );
                        // Question::where('id', $quesId)->update([
                        //     'curriculum_item' => $curriculum->id,
                        //     'index' => $maxIndex,
                        // ]);
                    }
                }

                return response()->json([
                    'moved' => true,
                ]);
            }
        }

        return response()->json([
            'moved' => false,
        ]);
    }

    public function addQuestionById(Request $request)
    {
        if (!Auth::user()->can('create_question')) {
            abort(403);
        }

        if ($request->curriculumId && $request->questionId) {
            $check = CurriculumQuestion::where('curriculum_item', $request->curriculumId)
                ->where('question_id', $request->questionId)
                ->first();

            if (!$check) {
                $indexInCurriculumQuestionTable = CurriculumQuestion::where('curriculum_item', $request->curriculumId)
                    ->max('index') ?: 0;

                CurriculumQuestion::create([
                    'curriculum_item' => $request->curriculumId,
                    'question_id' => $request->questionId,
                    'status' => 1,
                    'index' => $indexInCurriculumQuestionTable + 1,
                ]);
            }

            return response()->json([
                'added' => true,
            ]);
        }

        return response()->json([
            'added' => false,
        ]);
    }

    // public function handleTitleQuestion($text)
    // {
    //     $text = str_replace('\(', '', $text);
    //     $text = str_replace('\)', '', $text);
    //     $text = str_replace('\[', '', $text);
    //     $text = str_replace('\]', '', $text);
    //     $text = str_replace('\;', '', $text);
    //     $text = str_replace('\Rightarrow', 'suy ra', $text);
    //     $text = str_replace('\sqrt', 'căn bậc hai', $text);
    //     $text = str_replace('\ge', 'lớn hơn hoặc bằng', $text);
    //     $text = str_replace('\le', 'nhỏ hơn hoặc bằng', $text);
    //     $text = str_replace('\left( {', '(', $text);
    //     $text = str_replace('} \right)', ')', $text);
    //     // xử lý phân số
    //     $text = preg_replace('/\\frac\s*({([^{}]++|(?1))*})({([^{}]++|(?2))*})/', '$2/$4', $text);

    //     return $text;
    // }

    public function cleanString($text) {
        $matchArr = array(
            '\Rightarrow' => '',
            '\sqrt' => '',
            '\left' => '',
            '\right' => '',
            '\begin' => '',
            '\end' => '',
            '\frac' => '',
            '{array}' => '',
            '\forall' => '',
            '\in' => '',
            '\mathbb' => '',
            '\Leftrightarrow' => '',
            '{l}' => '',
            '\ge' => '',
            '\le' => '',
        );

        return str_replace(array_keys($matchArr), array_values($matchArr), $text);
    }

    public function importQuestion(Request $request)
    {
        $request->validate([
            'curriculum_item' => 'required',
            'owner' =>  'required',
            'file' => ['required', 'mimes:' . config('document.types'), 'max:' . config('document.max_size')],
        ]);

        $curriculum = CourseCurriculumItems::find($request->curriculum_item);

        if ($curriculum) {
            DB::beginTransaction();

            $maxIndex = Question::where('curriculum_item', $curriculum->id)->max('index') ?? 0;

            try {
                $response = $this->makeRequest(
                    config('app.tool_api_domain'),
                    'POST',
                    '/api/file/upload',
                    [],
                    [
                        'type' => 'cau_hoi',
                        'question_flag' => $request->question_flag,
                        'reason_flag' => $request->reason_flag,
                        'content_common' => $request->content_common,
                    ],
                    [
                        'file' => $request->file('file'),
                    ]
                );

                $responseArr = json_decode($response, true);
                $data = $responseArr['data'] ?? [];
                $images = $responseArr['images'] ?? [];

                if (!empty($data)) {
                    $baseUrl = env('MIX_APP_URL');
                    $imagePath = config('app.key_save_video') . '/images/' . time();
                    $dirname = '';
                    $newDirname = '';

                    if (!empty($images)) {
                        $newDirname = $baseUrl . $imagePath;
                        $imageLocation = public_path() . '/' . $imagePath;

                        if (!File::exists($imageLocation)) {
                            File::makeDirectory($imageLocation, 0775, true, true);
                        }

                        foreach ($images as $image) {
                            if (!$dirname) {
                                $dirname = dirname($image);
                            }

                            $contents = file_get_contents($image);
                            $imagename = substr($image, strrpos($image, '/') + 1);

                            file_put_contents($imageLocation . '/' . $imagename,  $contents);
                        }
                    }

                    foreach ($data as $d) {
                        $questions = $d['question'] ?? [];

                        if (empty($questions)) continue;

                        foreach($questions as $key => $questionRaw) {
                            ++$maxIndex;
                            $content = $questionRaw['content'];
                            $title = strip_tags($content);
                            $slug = str_to_slug($this->cleanString($title));
                            $check = Question::where('slug', $slug)->first();

                            while ($check) {
                                $slug = $slug . '-' . str_random(5);
                                $check =  Question::where('slug', $slug)->first();
                            }

                            $question = Question::create([
                                'content' => remove_font_family(replacePath($content, $dirname, $newDirname), '', true),
                                'curriculum_item' => $curriculum->id,
                                'owner' => $request->owner,
                                'type' => $questionRaw['type'],
                                'reason' =>  remove_font_family(replacePath($questionRaw['reason'], $dirname, $newDirname), '', true),
                                'title' => $title,
                                'slug' => $slug,
                                'index' => $maxIndex,
                                'import_origin' => 'file',
                            ]);

                            if (!empty($questionRaw['options'])) {
                                $answers = [];

                                foreach ($questionRaw['options'] as $answerRaw) {
                                    if ($answerRaw['content']) {
                                        $answers[] = [
                                            'question' => $question->id,
                                            'content' => remove_font_family(replacePath($answerRaw['content'], $dirname, $newDirname), '', true),
                                            'answer' => $answerRaw['answer'],
                                            'created_at' => now(),
                                            'updated_at' => now(),
                                        ];
                                    }
                                }

                                Answer::insert($answers);
                            }

                            CurriculumQuestion::create([
                                'curriculum_item' => $question->curriculum_item,
                                'question_id' => $question->id,
                                'index' => $question->index,
                                'status' => 1,
                            ]);
                        }
                    }

                    $curriculum->update(['enter_by' => 1]);

                    DB::commit();

                    return response()->json([
                        'imported' => true,
                        'message' => 'Thành công! Vui lòng check lại.',
                    ]);
                } else {
                    return response()->json([
                        'imported' => false,
                        'message' => 'Không đọc được nội dung hoặc file không đúng cú pháp.',
                    ]);
                }
            } catch (\Exception $e) {
                DB::rollback();

                return response()->json([
                    'imported' => false,
                    'message' => $e->getMessage(),
                ]);
            }
        }

        return response()->json([
            'imported' => false,
            'message' => 'Không tìm thấy bộ đề !!!',
        ]);
    }
}
