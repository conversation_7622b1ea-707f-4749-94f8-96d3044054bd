<?php

namespace MultipleChoices\Repositories;

use Illuminate\Support\Facades\DB;
use MultipleChoices\Models\Answer;
use MultipleChoices\Models\Question;
use MultipleChoices\Models\OldAnswer;
use MultipleChoices\Models\OldQuestion;
use MultipleChoices\Models\CurriculumQuestion;
use Course\Models\CourseCurriculumItems;
use Prettus\Repository\Eloquent\BaseRepository;

class QuestionRepository extends BaseRepository
{
    public function model()
    {
        return Question::class;
    }

    protected function countAllQuestionInCurriculum($cId)
    {
        return $this->findWhere(['curriculum_item' => $cId])->count();
    }

    public function addQuestion($data)
    {
        DB::beginTransaction();

        try {
            // logic cũ: curriculum_item và question quan hệ 1 - n
            // logic mới: curriculum_item và question quan hệ n - n
            // hiện vẫn lưu lại cột curriculum_item trong bảng question để xem câu hỏi được tạo bắt nguồn từ curriculum_item nào.
            $indexInQuestionsTable = $this->countAllQuestionInCurriculum($data['curriculum_item']);

            if ($indexInQuestionsTable >= config('web.max_que_in_exam')) {
                return false;
            }

            $questionData = [
                'content' => remove_font_family($data['content'], '', true),
                'curriculum_item' => $data['curriculum_item'],
                'related_lecture' => $data['related_lecture'] ?? null,
                'owner' => $data['owner'],
                'type' => $data['type'],
                'reason' => remove_font_family($data['reason'], '', true),
                'title' => $data['title'],
                'slug' => $data['slug'],
                'index' => $indexInQuestionsTable,
                'ads_url' => $data['adsUrl'] ?? null,
                'text_select' => $data['text_select'] ?? null,
            ];
            $question = $this->create($questionData);
            $answers = [];

            if ($data['answers']) {
                foreach ($data['answers'] as $key => $answer) {
                    if ($answer['content']) {
                        $answers[] = [
                            'question' => $question->id,
                            'content' => remove_font_family($answer['content'], '', true),
                            'answer' => $answer['answer'],
                            'created_at' => now(), // insert function not auto add created_at, updated_at column
                            'updated_at' => now(),
                        ];
                    }
                }

                Answer::insert($answers);
            }

            // logic cho quan hệ n - n
            $indexInCurriculumQuestionTable = CurriculumQuestion::where('curriculum_item', $question->curriculum_item)
                ->max('index') ?: 0;

            CurriculumQuestion::create([
                'curriculum_item' => $question->curriculum_item,
                'question_id' => $question->id,
                'index' => $indexInCurriculumQuestionTable + 1,
                'status' => 1,
            ]);

            DB::commit();

            return $question;
        } catch (\Exception $e) {
            DB::rollback();

            return false;
        }
    }

    /**
     * @param $data
     *
     * @return mixed
     */
    public function updateQuestion($data)
    {
        DB::beginTransaction();

        try {
            $questionId = $data['question_id'];

            $question = Question::find($questionId);

            if (!$question) {
                throw new \Exception('Question not found!');
            }

            // Save history change question
            $this->createOldQuestion($question);

            $questionUpdated = $this->update([
                'content' => remove_font_family($data['content'], '', true),
                'related_lecture' => $data['related_lecture'] ?? $question->related_lecture,
                'owner' => $data['owner'] ?? auth()->id(),
                'type' => $data['type'],
                'reason' => remove_font_family($data['reason'], '', true),
                'title' => $data['title'],
                'slug' => $data['slug'],
                'ads_url' => $data['adsUrl'] ?? $question->adsUrl,
                'edited_at' => now(),
                'text_select' => $data['text_select'],
            ], $questionId);

            // Delete all old answer
            Answer::where('question', $questionUpdated->id)->delete();

            // Add new question
            $answers = [];

            if ($data['answers']) {
                foreach ($data['answers'] as $key => $answer) {
                    if ($answer['content']) {
                        $answers[] = [
                            'question' => $questionUpdated->id,
                            'content' => remove_font_family($answer['content'], '', true),
                            'answer' => $answer['answer'],
                            'created_at' => now(), // insert function not auto add created_at, updated_at column
                            'updated_at' => now(),
                        ];
                    }
                }

                Answer::insert($answers);
            }

            DB::commit();

            return $questionUpdated;
        } catch (\Exception $e) {
            DB::rollBack();

            return false;
        }
    }

    /**
     * @param $data
     */
    public function onSortEnd($data)
    {
        // foreach ($data as $k => $value) {
        //     $this->update(['index' => $k], $value['id']);
        // }
        DB::beginTransaction();

        try {
            foreach ($data as $k => $value) {
                CurriculumQuestion::where('curriculum_item', $value['curriculum_item'])
                    ->where('question_id', $value['id'])
                    ->update(['index' => $k]);
            }

            DB::commit();

            return true;
        } catch (\Exception $e) {
            DB::rollBack();

            return false;
        }
    }

    private function createOldQuestion($question)
    {
        // Chỉ lưu lại 10 bản updated cũ và 1 ngày chỉ lưu 1 bản updated cuối cùng
        $date = date('Y-m-d');
        $limit = 10;
        $count_updated = OldQuestion::where('question_id', $question->id)->count();

        $oldQuestion = OldQuestion::where('question_id', $question->id)
            ->whereDate('created_at', $date)
            ->where('status', '<>', 'restore')
            ->orderBy('id', 'desc')
            ->first();

        if ($count_updated > $limit) {
            OldQuestion::where('question_id', $question->id)
                ->orderBy('id')
                ->first()
                ->delete();
        }

        $oldData = [
            'question_id' => $question->id,
            'content' => $question->content,
            'reason' => $question->reason,
            'owner' => $question->owner,
            'title' => $question->title,
            'slug' => $question->slug,
            'text_select' => $question->text_select,
        ];

        if ($question->text_select !== null) {
            $curriculum_item = CourseCurriculumItems::find($question->curriculum_item);

            if (!empty($curriculum_item->quiz_content[$question->text_select])) {
                $oldData['paragraph'] = $curriculum_item->quiz_content[$question->text_select]['content'];
            }
        } else {
            $oldData['paragraph'] = null;
        }

        if ($oldQuestion) {
            $oldQuestion->update($oldData);
        } else {
            $maxOldIndex = OldQuestion::where('question_id', $question->id)->max('index');
            $oldData['index'] = $maxOldIndex ? $maxOldIndex + 1 : 1;
            $oldQuestion = OldQuestion::create($oldData);
        }

        $question->load('getAnswer');

        if ($question->getAnswer->count() > 0) {
            // Delete all old answer
            OldAnswer::where('old_question_id', $oldQuestion->id)->delete();

            $oldAnswers = [];

            foreach ($question->getAnswer as $answer) {
                $oldAnswers[] = [
                    'old_question_id' => $oldQuestion->id,
                    'content' => $answer->content,
                    'answer' => $answer->answer,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }

            OldAnswer::insert($oldAnswers);
        }
    }

    public function restoreQuestion($oldQuestion)
    {
        DB::beginTransaction();

        try {
            $date = date('Y-m-d');
            $question = Question::find($oldQuestion->question_id);

            if (!$question) {
                throw new \Exception('Question not found!');
            }

            $oldData = [
                'question_id' => $question->id,
                'content' => $question->content,
                'reason' => $question->reason,
                'owner' => $question->owner,
                'title' => $question->title,
                'slug' => $question->slug,
                'text_select' => $question->text_select,
                'status' => 'restore',
            ];

            $quiz_content = [];
            $text_select = null;
            $curriculum_item = CourseCurriculumItems::find($question->curriculum_item);

            if ($curriculum_item) {
                $quiz_content = $curriculum_item->quiz_content;

                if (!empty($curriculum_item->quiz_content[$question->text_select])) {
                    $oldData['paragraph'] = $curriculum_item->quiz_content[$question->text_select]['content'];
                }
            }

            if ($oldQuestion->paragraph) {
                if (!empty($oldData['paragraph'])) {
                    if ($oldQuestion->text_select == $question->text_select) {
                        if (strlen($oldQuestion->paragraph) != strlen($oldData['paragraph'])) {
                            $quiz_content[$oldQuestion->text_select]['content'] = $oldQuestion->paragraph;
                            $text_select = $oldQuestion->text_select;
                        } else {
                            $text_select = $oldQuestion->text_select;
                        }
                    } else {
                        if (isset($quiz_content[$oldQuestion->text_select])) {
                            if (strlen($oldQuestion->paragraph) == strlen($quiz_content[$oldQuestion->text_select]['content'])) {
                                $text_select = $oldQuestion->text_select;
                            } else {
                                $quiz_content[$oldQuestion->text_select]['content'] = $oldQuestion->paragraph;
                                $text_select = $oldQuestion->text_select;
                            }
                        } else {
                            $quiz_content[] = [
                                'id' => time(),
                                'content' => $oldQuestion->paragraph,
                            ];
                            $text_select = count($quiz_content) - 1;
                        }
                    }
                } else {
                    if (isset($quiz_content[$oldQuestion->text_select])) {
                        $text_select = $oldQuestion->text_select;
                    } else {
                        $quiz_content[] = [
                            'id' => time(),
                            'content' => $oldQuestion->paragraph,
                        ];
                        $text_select = count($quiz_content) - 1;
                    }
                }
            }

            if ($text_select !== null && $curriculum_item) {
                $curriculum_item->update(['quiz_content' => $quiz_content]);
            }

            $restoreQuestion = OldQuestion::where('question_id', $question->id)
                ->whereDate('created_at', $date)
                ->where('status', 'restore')
                ->orderBy('id', 'desc')
                ->first();
            $maxOldIndex = OldQuestion::where('question_id', $question->id)->max('index');

            if (!$restoreQuestion || ($restoreQuestion && $restoreQuestion->index < $maxOldIndex)) {
                $oldData['index'] = $maxOldIndex ? $maxOldIndex + 1 : 1;
                $restoreQuestion = OldQuestion::create($oldData);

                $question->load('getAnswer');

                if ($question->getAnswer->count() > 0) {
                    $oldAnswers = [];

                    foreach ($question->getAnswer as $answer) {
                        $oldAnswers[] = [
                            'old_question_id' => $restoreQuestion->id,
                            'content' => $answer->content,
                            'answer' => $answer->answer,
                            'created_at' => now(),
                            'updated_at' => now(),
                        ];
                    }

                    OldAnswer::insert($oldAnswers);
                }
            }

            $question->update([
                'content' => $oldQuestion->content,
                'reason' => $oldQuestion->reason,
                'owner' => auth()->id(),
                'title' => $oldQuestion->title,
                'slug' => $oldQuestion->slug,
                'text_select' => $text_select,
                'edited_at' => now(),
            ]);

            // Delete all old answer
            Answer::where('question', $question->id)->delete();

            $oldQuestion->load('oldAnswers');

            if ($oldQuestion->oldAnswers->count() > 0) {
                $answers = [];

                foreach ($oldQuestion->oldAnswers as $answer) {
                    $answers[] = [
                        'question' => $question->id,
                        'content' => $answer->content,
                        'answer' => $answer->answer,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ];
                }

                Answer::insert($answers);
            }

            DB::commit();

            return 'Khôi phục thành công!';
        } catch (\Exception $e) {
            DB::rollBack();

            return $e->getMessage();
        }
    }
}
