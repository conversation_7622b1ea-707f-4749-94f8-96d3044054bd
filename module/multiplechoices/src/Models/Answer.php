<?php

namespace MultipleChoices\Models;

use Illuminate\Database\Eloquent\Model;

class Answer extends Model
{
    const WRONG = 'N';
    const RIGHT = 'Y';

    protected $table = 'answers';
    protected $guarded = [];
    protected $fillable = [
        'content',
        'parsed_content',
        'parsed_content_status',
        'question',
        'created_at',
        'answer',
    ];

    public function setQuestionAttribute($id)
    {
        $this->attributes['question'] = intval($id);
    }

    public function getQuestion()
    {
        return $this->belongsTo(Question::class, 'question');
    }
}
