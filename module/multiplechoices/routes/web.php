<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Routing\Router;

$adminRoute = config('base.admin_route');
$moduleRoute = 'multiplechoices';

Route::group(['prefix' => $adminRoute, 'middleware' => 'check-role-manage'], function (Router $router) use ($adminRoute, $moduleRoute) {
    // ajax call
    $router->group(['prefix' => 'services'], function (Router $router) use ($moduleRoute) {
        $router->group(['prefix' => $moduleRoute], function (Router $router) {
            $router->group(['prefix' => 'question'], function (Router $router) {
                $router->get('{id}', 'QuestionController@getQuestion');
                $router->post('{id}/update', 'QuestionController@updateQuestionById')
                    ->middleware('permission:edit_question');
                $router->post('add-question', 'Question<PERSON>ontroller@addQuestion');
                $router->post('update-question', 'QuestionController@updateQuestion');
                $router->post('delete-question', 'QuestionController@deleteQuestion');
                $router->post('update-on-sort-end', 'QuestionController@onSortEnd');
                $router->post('move-question', 'QuestionController@moveQuestion');
                $router->post('add-question-by-id', 'QuestionController@addQuestionById');
            });

            $router->group(['prefix' => 'old-question'], function (Router $router) {
                $router->get('{question_id}', 'QuestionController@getOldQuestion');
                $router->post('{question_id}/restore/{old_question_id}', 'QuestionController@restoreOldQuestion');
            });
        });
    });
});
