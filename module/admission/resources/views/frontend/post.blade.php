@extends('nqadmin-dashboard::frontend.master')

@php
    $isExamPost = $admissionPost->type == \Admission\Models\AdmissionPost::TYPE_EXAM;
    $routeName = $isExamPost ? 'front.admission.post' : 'front.admission.school';
    $thumbnail = asset($admissionPost->thumbnail ?: ($admission->thumbnail ?? 'images/banner-sm.jpg'));
    $postTypes = array_column(\Admission\Models\AdmissionPost::postTypes(), 'label', 'value');
@endphp

@section('title', $admissionPost->name)
@section('seo_title', $admissionPost->seo_title)
@section('seo_description', $admissionPost->seo_description)
@section('seo_keywords', $admissionPost->seo_keywords)
@section('og_title', $admissionPost->seo_title)
@section('og_description', $admissionPost->seo_description)
@section('og_image', $thumbnail)
@section('og_type', 'article')

@section('snippet')
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [{
            "@type": "ListItem",
            "position": 1,
            "name": "{{ $isExamPost ? 'Đáp án - Đề thi' : 'Tuyển sinh' }}",
            "item": "{{ route($isExamPost ? 'front.admission.post-exam-index' : 'front.admission.index') }}"
        }, {
            "@type": "ListItem",
            "position": 2,
            "name": "{{ $admission ? ($admission->code ?: $admission->name) : $admissionPost->name }}",
            "item": "{{ $admission ?
                route('front.admission.school', ['id' => $admission->id, 'slug' => $admission->slug]) :
                route('front.admission.post', ['id' => $admissionPost->id, 'slug' => $admissionPost->slug])
            }}"
        }]
    }
    </script>
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "AggregateRating",
        "ratingValue": "{{ config('rating.avg_default') }}",
        "ratingCount": "{{ ceil(20 / 100 * $admissionPost->view) ?: 1 }}",
        "itemReviewed": {
            "@type": "CreativeWorkSeries",
            "name": "{{ $admissionPost->name }}",
            "image": "{{ $thumbnail }}"
        }
    }
    </script>
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "NewsArticle",
        "headline": "{{ $admissionPost->name }}",
        "image": [
            "{{ $thumbnail }}"
        ],
        "datePublished": "{{ date(DATE_ISO8601, strtotime($admissionPost->updated_at)) }}",
        "dateModified": "{{ date(DATE_ISO8601, strtotime($admissionPost->updated_at)) }}",
        "author": [{
            "@type": "Organization",
            "name": "{{ config('app.name') }}",
            "url": "{{ config('app.url') }}"
        }]
    }
    </script>
@endsection

@push('css')
    <link rel="stylesheet" href="{{ mix_cdn('css/frontend/editor-custom.css') }}" />
@endpush

@push('js')
    <script src="{{ mix_cdn('js/frontend/editor-custom.js') }}"></script>
@endpush

@section('content')
    @include('nqadmin-admission::frontend.partials.search-admission', [
        'type' => $admissionPost->type,
        'keyword' => $admissionPost->name,
        'banner' => (!$isExamPost && $admission && $admission->banner) ? asset($admission->banner) : null,
    ])
    <div class="main-page">
        @if ($topPosts->count() > 0)
        <aside class="lsidebar" id="lsidebar">
            <div class="lsidebar-main">
                <div class="lsidebar-wrapper">
                    <button class="btn btn-default btn-lsidebar toggle-lsidebar" type="button">
                       <i class="fas fa-indent"></i>
                    </button>
                    <div class="lsidebar-body">
                        <div class="lsidebar-title">
                            Thông tin tuyển sinh
                        </div>
                        <div class="lsidebar-nav scroll-tree p-0">
                            <ul class="list-unstyled">
                                @foreach($topPosts as $key => $topPostArr)
                                    <li class="bg-white border-0 mb-0 pb-0 pt-0">
                                        <a class="root-nav" href="#sidebar_menu_{{ $key }}" data-toggle="collapse" aria-expanded="true">
                                            <span>{{ $postTypes[$key] }}</span>
                                        </a>
                                        <ul class="list-unstyled collapse in" id="sidebar_menu_{{ $key }}">
                                            @foreach($topPostArr as $topP)
                                                <li>
                                                    <a class="leaf-nav" style="margin-left: 30px;" href="{{ route('front.admission.post', ['id' => $topP->id, 'slug' => $topP->slug]) }}">
                                                        {{ $topP->name }}
                                                    </a>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </aside>
        @endif
        <div class="box-wrapper bg-white">
            <div class="box-wrapper-header bg-light">
                <div class="breadcrumb-wrapper container">
                    <ol class="breadcrumb">
                        <li>
                            <a class="has-underline" href="{{ route('front.home.index.get') }}" title="Trang chủ"><i class="fas fa-home"></i></a>
                        </li>
                        <li>
                            <a class="has-underline" href="{{ route($isExamPost ? 'front.admission.post-exam-index' : 'front.admission.index') }}">
                                {{ $isExamPost ? 'Đáp án - Đề thi' : 'Tuyển sinh' }}
                            </a>
                        </li>
                        @if($admission)
                        <li>
                            <a class="has-underline" href="{{ route('front.admission.school', ['id' => $admission->id, 'slug' => $admission->slug]) }}">
                                {{ $admission->code ?: $admission->name }}
                            </a>
                        </li>
                        @endif
                    </ol>
                </div>
            </div>
            <div class="box-wrapper-body container p-sm-0">
                <div class="post-main">
                    <div class="post-header">
                        @if (!$isExamPost && $admission && $admission->thumbnail)
                            <img class="post-thumbnail" src="{{ asset($admission->thumbnail) }}" alt="{{ $admission->name }}">
                        @endif
                        <h1 class="post-title mt-0 js-post-title" data-category_id="{{ $admissionPost->id }}">
                            {{ $admissionPost->name }}
                            @if (auth()->check() && auth()->user()->isEditor() && auth()->user()->can('admission_edit'))
                                <a class="btn btn-link" target="_blank"
                                href="{{ route('nqadmin::admission_post.edit.get', ['id' => $admissionPost->id]) }}"
                                >
                                    <i class="fa fa-edit ml-0"></i>
                                </a>
                            @endif
                        </h1>
                    </div>
                    @if ($topPosts->count() > 0)
                    <nav class="nav-menu-list pl-md-3">
                        <ul class="links">
                            <li>
                                <a href="{{ route('front.admission.school', ['id' => $admission->id, 'slug' => $admission->slug]) }}">
                                    Tuyển sinh {{ date("Y") }}
                                </a>
                            </li>
                            @foreach($topPosts as $topPost)
                                @php
                                    $firstPost = $topPost->first();
                                @endphp
                                <li class="{{ $firstPost->id == $admissionPost->id ? 'active' : '' }}">
                                    <a href="{{ route('front.admission.post', ['id' => $firstPost->id, 'slug' => $firstPost->slug]) }}">
                                        {{ $postTypes[$firstPost->type] }}
                                    </a>
                                </li>
                            @endforeach
                        </ul>
                        <button class="hidden" count="0" type="button">
                            <i class="fas fa-bars"></i> Xem thêm
                        </button>
                        <ul class="hidden-links hidden"></ul>
                    </nav>
                    @endif
                    <div class="post-des">
                        {!! $admissionPost->description !!}
                    </div>
                    @if ($yearPosts->count() > 0)
                        <div class="year mb-1">
                            <form>
                                <strong>Năm:</strong>
                                <select name="p" class="form-control select-year p-0" onchange="this.form.submit()">
                                    @foreach($yearPosts as $yearPost)
                                        <option value="{{ $yearPost->id }}" {{ $yearPost->id == $admissionPost->id ? 'selected' : '' }}>{{ $yearPost->year }}</option>
                                    @endforeach
                                </select>
                            </form>
                        </div>
                    @endif
                    @include('nqadmin-dashboard::frontend.components.toolbox', ['dataItem' => $admissionPost])
                    <hr>
                    <div class="detail-action">
                        <a href="" class="btn btn-success prev-btn"><i class="fa fa-angle-left mr-2"></i> Trang trước</a>
                        @include('nqadmin-dashboard::frontend.components.like-share-facebook', ['customClass' => 'd-none'])
                        <a href="" class="btn btn-success next-btn">Trang sau <i class="fa fa-angle-right ml-2"></i>&nbsp;</a>
                    </div>
                    <hr>
                    @if (Agent::isMobile())
                        <div id="sidenav-toc" class="sidenav-toc toc-mobi mt-20">
                            <div class="sidebar-toc">
                                <p class="title" style="margin: 0 !important; font-size: 16px !important;">
                                    Nội dung bài viết
                                </p>
                                <ul id="toc" class="toc toc-nav-list"></ul>
                                <a href="javascript:;" class="btn-show-toc">
                                    <i class="fa fa-angle-up"></i>
                                    <span>Xem thêm</span>
                                </a>
                            </div>
                        </div>
                    @endif
                    <div class="post-content" id="post-content">
                        {!! $admissionPost->content !!}
                    </div>
                    @if ($admissionPost->document)
                        <div class="download-footer text-center mb-20">
                            <h5 style="color: red; font-size: 18px; font-weight: 600">
                                Để xem toàn bộ tài liệu, vui lòng tải xuống
                            </h5>
                            <a
                                class="btn btn-warning"
                                href="{{ route('front.admission.download', $admissionPost) }}"
                                style="padding: 5px 15px; font-weight: bold; font-size: 18px; color: #fff !important;"
                            >
                                <i class="fa fa-download" aria-hidden="true"></i> Tải xuống
                            </a>
                        </div>
                    @endif
                    @include('nqadmin-dashboard::frontend.components.toolbox', ['dataItem' => $admissionPost])
                    <hr>
                    <div class="detail-action">
                        <a href="" class="btn btn-success prev-btn"><i class="fa fa-angle-left mr-2"></i> Trang trước</a>
                        @include('nqadmin-dashboard::frontend.components.like-share-facebook', ['customClass' => 'd-none'])
                        <a href="" class="btn btn-success next-btn">Trang sau <i class="fa fa-angle-right ml-2"></i>&nbsp;</a>
                    </div>
                    <hr>
                    @if ($relateds->count() > 0)
                        <div class="related-school mt-20 mb-20">
                            <h4 class="text-related mb-20">Có thể bạn quan tâm:</h4>
                            <div class="list-enroll">
                                <div class="owl-stage-outer">
                                    @foreach ($relateds as $index => $related)
                                        <div class="owl-item {{ $index > 0 ? 'ml-4' : ''}}" >
                                            <a
                                                class="box-product"
                                                href="{{ route($routeName, ['id' => $related->id, 'slug' => $related->slug]) }}"
                                                title="{{ $related->name }}"
                                            >
                                                <div class="item-img">
                                                    <img
                                                        class="ls-is-cached"
                                                        src="{{ asset($related->thumbnail ?: 'images/banner-exam-default.jpg') }}"
                                                        onerror="this.src='{{asset_cdn('images/banner-exam-default.jpg')}}'"
                                                        alt="{{ $related->name }}"
                                                    >
                                                </div>
                                                <h3>{{ $related->name }}</h3>
                                            </a>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            @if ($admission)
                                <span class="view-all-school">
                                    <a class="btn-view-all mt-3" href="{{ route('front.admission.area', ['level' => $admission->admissionLevel->slug, 'area' => $admission->admissionArea->slug]) }}">Xem thêm </a>
                                </span>
                            @endif
                        </div>
                    @endif
                    <div class="mb-20">
                        <div class="fb-comments" data-href="{{url()->full()}}" data-width="100%" data-numposts="5"></div>
                    </div>
                </div>
                @if (!Agent::isMobile())
                    <div class="rsidebar">
                        <div class="rsidebar-wrapper">
                            <div id="sidenav-toc" class="sidenav-toc">
                                <div class="sidebar-toc">
                                    <p class="title">
                                        Nội dung bài viết
                                    </p>
                                    <ul id="toc" class="toc toc-nav-list">
                                    </ul>
                                    <a href="javascript:;" class="btn-show-toc">
                                        <i class="fa fa-angle-up"></i>
                                        <span>Xem thêm</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
            @if ($topPosts->count() > 0)
            <div class="navigation-mb">
                <button type="button" class="btn btn-warning toggle-lsidebar">
                    <i class="fas fa-list-ul mr-5"></i>
                    <span>Thông tin tuyển sinh</span>
                </button>
            </div>
            @endif
        </div>
    </div>
@endsection

@section('js')
    <ins class="982a9496" data-key="fa32779895fc725fd597ef0b5bb0707b"></ins>
    <script async defer src="https://aj1559.online/ba298f04.js"></script>
    <script src="{{ asset_cdn('libs/affs.js') }}?id=4"></script>
@endsection
@push('js-head')
    <script>
        var sLinkAff = 'https://s.shopee.vn/702mDOpnWt';
    </script>
@endpush
