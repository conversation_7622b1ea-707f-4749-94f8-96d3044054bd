@section('css')
    <link rel="stylesheet" href="{{ asset_cdn('libs/select2/dist/css/select2.min.css') }}" />
    <link rel="stylesheet" href="{{ mix_cdn('css/frontend/select2.css') }}" />
@endsection

@section('js')
    <script src="{{ asset_cdn('libs/select2/dist/js/select2.full.min.js') }}"></script>
    <script src="{{ asset_cdn('js/frontend/select2.js') }}"></script>
    <script>
        $('#user_id').select2({
            minimumInputLength: 0,
            width: '100%',
            allowClear: true,
        });
    </script>
@endsection

@if (count($errors) > 0)
    @foreach($errors->all() as $e)
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <strong>Lỗi!</strong> {{ $e }}
        </div>
    @endforeach
@endif

{!! \Base\Supports\FlashMessage::renderMessage('edit') !!}

{{ Form::open([
    'url' => $action,
    'method' => 'post',
    'enctype' => 'multipart/form-data',
    'class' => 'form-admission',
    'autocomplete' => 'off',
]) }}
<div class="row">
    <div class="col-md-12">
        <div class="form-group required {{ has_error('name', $errors) }}">
            {{ Form::label('name', 'Tiêu đề') }}
            {{ Form::text('name', $admissionPost->name ?? '', ['class' => 'form-control', 'required' => true, 'autofocus' => true, 'placeholder' => 'Tiêu đề']) }}
            <span class="help-block">{{ $errors->first('name') }}</span>
        </div>

        <div class="form-group required {{ has_error('slug', $errors) }}">
            {{ Form::label('slug', 'Slug (Tối đa 70 ký tự)') }}
            <button type="button" class="btn btn-info float-right m-1 js-render-slug" style="padding: 1px 3px;">
                <small>Render slug</small>
            </button>
            {{ Form::text('slug', $admissionPost->slug ?? '', [
                'class' => 'form-control',
                'required' => true,
                'autofocus' => true,
                'placeholder' => 'Slug',
                'pattern' => "^[A-Za-z0-9]+(?:-[A-Za-z0-9]+)*$",
                'data-parsley-pattern-message' => 'Dữ liệu không phải là một slug'
            ]) }}
            <span class="help-block">{{ $errors->first('slug') }}</span>
        </div>

        <div class="form-group {{ has_error('description', $errors) }}">
            {!! Form::label('description', 'Mô tả') !!}
            {!! Form::textarea('description', $admissionPost->description ?? '', [
                'class' => 'form-control ckeditor',
                'required' => true,
                'placeholder' => 'Mô tả',
                'rows' => 3
            ]) !!}
            <span class="help-block">{{ $errors->first('description') }}</span>
        </div>
        <div class="form-group {{ has_error('content', $errors) }}">
            {!! Form::label('content', 'Chi tiết') !!}
            {!! Form::textarea('content', $admissionPost->content ?? '', [
                'class' => 'form-control editor-input-content',
                'data-url_upload_image' => route('nqadmin::admission.upload_image'),
                'required' => false,
                'placeholder' => 'Chi tiết',
                'rows' => 7,
                'id' => 'content'
            ]) !!}
            <span class="help-block">{{ $errors->first('description') }}</span>
        </div>

        <div class="form-group {{ has_error('seo_title', $errors) }}">
            {!! Form::label('seo_title', 'SEO title') !!}
            {{ Form::text('seo_title', $admissionPost->seo_title ?? '', ['class' => 'form-control', 'placeholder' => 'SEO title']) }}
            <span class="help-block">{{ $errors->first('seo_title') }}</span>
        </div>

        <div class="form-group {{ has_error('seo_description', $errors) }}">
            {!! Form::label('seo_description', 'SEO description') !!}
            {!! Form::textarea('seo_description', $admissionPost->seo_description ?? '', [
                'class' => 'form-control',
                'placeholder' => 'SEO description',
                'rows' => 3
            ]) !!}
            <span class="help-block">{{ $errors->first('seo_description') }}</span>
        </div>

        <div class="form-group {{ has_error('seo_keywords', $errors) }}">
            {!! Form::label('seo_keywords', 'SEO keywords') !!}
            {{ Form::text('seo_keywords', $admissionPost->seo_keywords ?? '', ['class' => 'form-control', 'placeholder' => 'SEO keywords']) }}
            <span class="help-block">{{ $errors->first('seo_keywords') }}</span>
        </div>

        <div class="form-check {{ has_error('status', $errors) }}">
            {{ Form::checkbox('status', $active, ($admissionPost->status ?? false), ['id' => 'status']) }}
            {!! Form::label('status', 'Duyệt bài') !!}
            <span class="help-block">{{ $errors->first('status') }}</span>
        </div>

        <div class="form-check {{ has_error('is_hot', $errors) }}">
            {{ Form::checkbox('is_hot', $active, ($admissionPost->is_hot ?? false), ['id' => 'is_hot']) }}
            {!! Form::label('is_hot', 'Nổi bật') !!}
            <span class="help-block">{{ $errors->first('is_hot') }}</span>
        </div>
    </div>

    <div class="col-md-4">
        <div class="form-group required">
            {!! Form::label('type', 'Loại tin tức') !!}
            {{ Form::select('type', [null => 'Chọn loại tin'] + array_column(\Admission\Models\AdmissionPost::postTypes(), 'label', 'value'), $admissionPost->type ?? null, [
                'class' => 'form-control select2',
                'required' => true,
            ]) }}
        </div>
        <div class="form-group">
            {!! Form::label('year', 'Năm') !!}
            {{ Form::select('year', config('web.years'), $admissionPost->year ?? null, [
                'class' => 'form-control select2',
                'id' => 'year',
                'data-placeholder' => 'Chọn năm',
            ]) }}
        </div>

        <div class="form-group {{ has_error('editor_id', $errors) }}">
            {{ Form::label('editor_id', 'Nhập liệu') }}
            {{ Form::select('editor_id', [], null, [
                'class' => 'form-control select2 js-select2-ajax',
                'id' => 'user_id',
                'data-placeholder' => 'Chọn nhập liệu',
                'data-ajax-url' => route('suggest.users', ['role' => 'nhap-lieu']),
                'required' => true,
                'data-ajax-selected-values' => $admissionPost->editor_id,
            ]) }}
            <span class="help-block">{{ $errors->first('editor_id') }}</span>
        </div>

        @include('nqadmin-dashboard::backend.components.thumbnail', ['imgPath' => $admissionPost->thumbnail])

        <label><strong>File đính kèm</strong></label>
        @if ($admissionPost->document)
            <a href="{{ asset($admissionPost->document) }}" target="_blank" title="Xem file" style="float: right">
                <i class="fa fa-eye"></i>
            </a>
        @endif
        @include('nqadmin-dashboard::backend.components.media-upload')

        <div class="form-group {{ has_error('subject_id', $errors) }}">
            {!! Form::label('subject_id', 'Môn') !!}
            {{ Form::select('subject_id', [null => 'Chọn môn__'] + $subjects, $admissionPost->subject_id ?? null, [
                'class' => 'form-control',
            ]) }}
            <span class="help-block">{{ $errors->first('subject_id') }}</span>
        </div>
        <div class="form-group {{ has_error('class_id', $errors) }}">
            {!! Form::label('class_id', 'Lớp') !!}
            {{ Form::select('class_id', [null => 'Chọn lớp__'] + $classes, $admissionPost->class_id ?? null, [
                'class' => 'form-control',
            ]) }}
            <span class="help-block">{{ $errors->first('class_id') }}</span>
        </div>
    </div>

    <div class="col-md-16">
        <div class="form-group">
            <button type="submit" class="btn btn-primary">
                <i class="fa fa-spinner fa-pulse js-loading-icon d-none"></i>
                {{ $isNew ? 'Tạo mới' : 'Cập nhật' }}
            </button>
            <div class="float-right">
                {{ Form::hidden('preUrl', $preUrl ?? $backUrl) }}
                <a class="btn btn-danger" href="{{ $backUrl }}">Quay lại</a>
            </div>
        </div>
    </div>
</div>
{{ Form::close() }}

@push('js')
    <script src="{{ asset_cdn('adminux/vendor/ckeditor/ckeditor.js') }}"></script>
    <script>
        @if ($isNew)
            $("[name='name']").keyup(function () {
                $("[name='slug']").val(window.helperFunc.titleToSlug($(this).val()));
                $("[name='seo_title']").val($(this).val());
            })
        @endif

        $('.js-render-slug').on('click', function() {
            $("[name='slug']").val(window.helperFunc.titleToSlug($("[name='name']").val()));
        })
    </script>
@endpush
