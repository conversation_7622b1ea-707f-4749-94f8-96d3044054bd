<?php

namespace Admission\Services;

use Admission\Models\AdmissionPost;
use Base\Services\File\FileService;
use Base\Services\Image\ImageService;
use Base\Traits\ModelFilterTrait;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Mews\Purifier\Facades\Purifier;

class AdmissionPostService
{
    use ModelFilterTrait;

    protected $imageService;
    protected $fileService;

    public function __construct(ImageService $imageService, FileService $fileService)
    {
        $this->imageService = $imageService;
        $this->fileService = $fileService;
    }

    public function list($conditions = [], $limit = null)
    {
        $searchable = [
            'name' => function ($query, $keyword) {
                $keyword = escapeLike($keyword);

                $query->where('name', 'like', '%' . $keyword . '%');
            },
            'type' => function ($query, $value) {
                $query->where('type', '=', $value);
            },
            'admission_id' => function ($query, $value) {
                $query->where('admission_id', $value);
            },
            'author_id' => function ($query, $value) {
                $query->where('author_id', $value);
            },
        ];

        $sortable = ['id', 'name'];

        $query = AdmissionPost::with('users:id,first_name');
        $user = \Auth::user();

        if (!$user->isSuperAdmin()) {
            $query->where('author_id', $user->id);
        }

        return $this->filterModels($query, $searchable, $sortable, ['id', 'DESC'], $conditions, $limit);
    }

    public function create($request)
    {
        list($bannerImagePath, $documentPath) = $this->uploadBannerAndDocument($request);

        $data = [
            'name' => $request->name,
            'slug' => $request->slug,
            'description' => Purifier::clean($request->description, 'full_html'),
            'content' => remove_font_family($request->content),
            'author_id' => \Auth::id(),
            'editor_id' => $request->editor_id ?: \Auth::id(),
            'admission_id' => $request->admission ?: null,
            'type' => $request->type ?: AdmissionPost::TYPE_DEFAULT,
            'year' => $request->year ?: null,
            'is_hot' => $request->is_hot ?? AdmissionPost::DISABLED,
            'seo_title' => $request->seo_title,
            'seo_description' => $request->seo_description,
            'seo_keywords' => $request->seo_keywords,
            'thumbnail' => $bannerImagePath ?: null,
            'document' => $documentPath ?: null,
//            'class_id' => $request->class_id ?: null,
//            'subject_id' => $request->subject_id ?: null,
        ];

        if ($request->type == AdmissionPost::TYPE_EXAM) {
            $data['class_id'] = $request->class_id ?: null;
            $data['subject_id'] = $request->subject_id ?: null;
        }

        return AdmissionPost::create($data);
    }

    public function update($id, $request)
    {
        $admissionPost = AdmissionPost::find($id);

        if ($admissionPost) {
            list($bannerImagePath, $documentPath) = $this->uploadBannerAndDocument($request);

            $data = [
                'name' => $request->name,
                'slug' => $request->slug,
                'description' => Purifier::clean($request->description, 'full_html'),
                'content' => remove_font_family($request->content) ?? '',
                'editor_id' => $request->editor_id ?: $admissionPost->editor_id,
                'type' => $request->type ?: AdmissionPost::TYPE_DEFAULT,
                'year' => $request->year ?: null,
                'status' => $request->status ?? $admissionPost->status,
                'is_hot' => $request->is_hot ?? $admissionPost->is_hot,
                'seo_title' => $request->seo_title,
                'seo_description' => $request->seo_description,
                'seo_keywords' => $request->seo_keywords,
                'thumbnail' => $bannerImagePath ?: $admissionPost->thumbnail,
                'document' => $documentPath ?: $admissionPost->document,
            ];

            if ($request->type == AdmissionPost::TYPE_EXAM) {
                $data['class_id'] = $request->class_id;
                $data['subject_id'] = $request->subject_id;
            }

            /*if ($request->content != $admissionPost->content || $request->name != $admissionPost->name) {
                $data['editor_id'] = $request->editor_id ?: $admissionPost->editor_id;
                $data['edited_at'] = Carbon::now();
            }*/

            $admissionPost->update($data);
        }

        return $admissionPost;
    }

    public function delete($request)
    {
        return AdmissionPost::where('id', $request->id)->delete();
    }

    public function uploadBannerAndDocument($request)
    {
        $bannerImagePath = $documentPath = null;

        if ($request->hasFile('thumbnail')) {
            $bannerImagePath = $this->imageService->upload('admission_post', $request->file('thumbnail'));
        }

        if ($request->hasFile('file')) {
            $documentPath = $this->fileService->upload('admission_post', $request->file('file'));
        }

        return [$bannerImagePath, $documentPath];
    }
}
