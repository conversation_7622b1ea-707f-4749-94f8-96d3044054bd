<?php

namespace Admission\Services;

use Admission\Models\Admission;
use Admission\Models\AdmissionArea;
use Admission\Models\AdmissionLevel;
use Base\Services\Image\ImageService;
use Base\Traits\ModelFilterTrait;
use Mews\Purifier\Facades\Purifier;

class AdmissionService
{
    use ModelFilterTrait;

    protected $imageService;

    public function __construct(ImageService $imageService)
    {
        $this->imageService = $imageService;
    }

    public function list($conditions = [], $limit = null)
    {
        $searchable = [
            'name' => function ($query, $keyword) {
                $keyword = escapeLike($keyword);

                $query->where('name', 'like', '%' . $keyword . '%');
            },
            'admission_level_id' => function ($query, $value) {
                $query->where('admission_level_id', '=', $value);
            },
            'admission_area_id' => function ($query, $value) {
                $query->where('admission_area_id', $value);
            },
            'author_id' => function ($query, $value) {
                $query->where('author_id', $value);
            },
        ];

        $sortable = ['id', 'name'];

        $query = Admission::with('users:id,first_name', 'admissionArea', 'admissionLevel');
        $user = \Auth::user();

        if (!$user->isSuperAdmin()) {
            $query->where('author_id', $user->id);
        }

        return $this->filterModels($query, $searchable, $sortable, ['id', 'DESC'], $conditions, $limit);
    }

    public function find($request)
    {
        return app(Admission::class)
            ->where('id', $request->admission)
            ->first();
    }

    public function create($request)
    {
        if ($request->hasFile('thumbnail')) {
            $thumbnailImagePath = $this->imageService->upload('admission', $request->file('thumbnail'));
        }

        if ($request->hasFile('banner')) {
            $bannerImagePath = $this->imageService->upload('admission', $request->file('banner'));
        }

        return Admission::create([
            'name' => $request->name,
            'code' => $request->code,
            'description' => Purifier::clean($request->description, 'full_html'),
            'content' => remove_font_family($request->content),
            'author_id' => \Auth::id(),
            'admission_area_id' => $request->admission_area_id,
            'admission_level_id' => $request->admission_level_id,
            'thumbnail' => $thumbnailImagePath ?? null,
            'banner' => $bannerImagePath ?? null,
            'status' => $request->status ?? Admission::DISABLED,
            'seo_title' => $request->seo_title,
            'seo_description' => $request->seo_description,
            'seo_keywords' => $request->seo_keywords,
        ]);
    }

    public function update($request)
    {
        try {
            if ($request->hasFile('thumbnail')) {
                $thumbnailImagePath = $this->imageService->upload('admission', $request->file('thumbnail'));
            }

            if ($request->hasFile('banner')) {
                $bannerImagePath = $this->imageService->upload('admission', $request->file('banner'));
            }

            $admission = Admission::find($request->id);

            if ($admission) {
                $admission->update([
                    'name' => $request->name,
                    'code' => $request->code,
                    'description' => Purifier::clean($request->description, 'full_html'),
                    'content' => remove_font_family($request->content) ?? '',
                    'author_id' => $request->author_id ?: \Auth::id(),
                    'admission_area_id' => $request->admission_area_id,
                    'admission_level_id' => $request->admission_level_id,
                    'thumbnail' => $thumbnailImagePath ?? $admission->thumbnail,
                    'banner' => $bannerImagePath ?? $admission->banner,
                    'status' => $request->status ?? $admission->status,
                    'seo_title' => $request->seo_title,
                    'seo_description' => $request->seo_description,
                    'seo_keywords' => $request->seo_keywords,
                ]);
            }

            return $admission;
        } catch (\Exception $e) {
            throw $e;
        }
    }

    public function delete($request)
    {
        return Admission::where('id', $request->id)->delete();
    }
}
