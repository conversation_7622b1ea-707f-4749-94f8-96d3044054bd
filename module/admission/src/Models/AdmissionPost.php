<?php

namespace Admission\Models;

use ClassLevel\Models\ClassLevel;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Subject\Models\Subject;
use Users\Models\Users;

class AdmissionPost extends Model
{
    const ACTIVE = 1;
    const DISABLED = 0;
    const IS_HOT = 1;

    const TYPE_DEFAULT = 0;
    const TYPE_BENCHMARK = 1;
    const TYPE_EXAM = 2;
    const TYPE_ADMISSION_PLAN = 3;
    const TYPE_ADMISSION_FEE = 4;
    const TYPE_MAJOR_CODE = 5;

    protected $table = 'admission_post';

    protected $fillable = [
        'id',
        'name',
        'slug',
        'description',
        'content',
        'author_id',
        'admission_id',
        'type',
        'year',
        'status',
        'index',
        'view',
        'seo_title',
        'seo_description',
        'seo_keywords',
        'thumbnail',
        'document',
        'class_id',
        'subject_id',
        'editor_id',
        'edited_at',
        'is_hot',
    ];

    public function admission()
    {
        return $this->belongsTo(Admission::class);
    }

    public function users()
    {
        return $this->belongsTo(Users::class, 'author_id', 'id');
    }

    public function editor()
    {
        return $this->belongsTo(Users::class, 'editor_id', 'id');
    }

    public function classLevel()
    {
        return $this->belongsTo(ClassLevel::class, 'class_id');
    }

    public function subject()
    {
        return $this->belongsTo(Subject::class, 'subject_id');
    }

    public static function postTypes()
    {
        return [
            [
                'label' => 'Điểm chuẩn',
                'value' => self::TYPE_BENCHMARK,
            ],
            [
                'label' => 'Học phí',
                'value' => self::TYPE_ADMISSION_FEE,
            ],
            [
                'label' => 'Phương án tuyển sinh',
                'value' => self::TYPE_ADMISSION_PLAN,
            ],
            [
                'label' => 'Mã ngành',
                'value' => self::TYPE_MAJOR_CODE,
            ],
            [
                'label' => 'Chi tiết tuyển sinh',
                'value' => self::TYPE_DEFAULT,
            ],
            [
                'label' => 'Đáp án - đề thi',
                'value' => self::TYPE_EXAM,
            ],
        ];
    }
}
