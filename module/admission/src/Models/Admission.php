<?php

namespace Admission\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Users\Models\Users;

class Admission extends Model
{
    const ACTIVE = 1;
    const DISABLED = 0;

    protected $table = 'admission';

    protected $fillable = [
        'id',
        'name',
        'code',
        'slug',
        'description',
        'content',
        'author_id',
        'admission_area_id',
        'admission_level_id',
        'thumbnail',
        'banner',
        'status',
        'index',
        'view',
        'seo_title',
        'seo_description',
        'seo_keywords',
    ];

    /**
     * Set the name and the readable slug.
     *
     * @param string $value
     */
    public function setNameAttribute($value)
    {
        $this->attributes['name'] = $value;

        $this->setUniqueSlug($value);
    }

    /**
     * Set the unique slug.
     *
     * @param $value
     * @param $extra
     */
    public function setUniqueSlug($value)
    {
        $slug = str_slug($value);

        if (static::whereSlug($slug)->where('id', '<>', $this->id)->exists()) {
            $this->setUniqueSlug($slug . '-' . str_random(5));

            return;
        }

        $this->attributes['slug'] = $slug;
    }

    public function admissionArea()
    {
        return $this->belongsTo(AdmissionArea::class);
    }

    public function admissionLevel()
    {
        return $this->belongsTo(AdmissionLevel::class);
    }

    public function users()
    {
        return $this->belongsTo(Users::class, 'author_id', 'id');
    }

    public function admissionPost()
    {
        return $this->hasMany(AdmissionPost::class, 'admission_id', 'id');
    }
}
