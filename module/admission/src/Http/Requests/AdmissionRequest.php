<?php

namespace Admission\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Admission\Models\Admission;
use Illuminate\Validation\Rule;

class AdmissionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules()
    {
        return [
            'name' => 'required',
            'code' => [
                'required',
                Rule::unique('admission', 'code')->ignore($this->query('id') ?? null),
                ],
            'admission_area_id' => 'required',
            'admission_level_id' => 'required',
            'thumbnail' => 'nullable|mimes:jpg,png,jpeg|max:10000',
            'banner' => 'nullable|mimes:jpg,png,jpeg|max:20000',
        ];
    }

    public function messages()
    {
        return [
            'name.unique' => __('Tiêu đề đã tồn tại'),
        ];
    }
}
