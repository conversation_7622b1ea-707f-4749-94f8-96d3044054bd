<?php

namespace Admission\Http\Controllers;

use App\Http\Controllers\Controller as BaseController;
use Admission\Models\AdmissionArea;
use Admission\Models\AdmissionLevel;
use Admission\Models\Admission;
use Admission\Services\AdmissionService;
use Illuminate\Http\Request;
use Admission\Http\Requests\AdmissionRequest;
use Base\Supports\FlashMessage;
use Base\Services\Image\ImageService;
use Illuminate\Validation\ValidationException;

class AdmissionController extends BaseController
{
    protected $admissionService;
    protected $imageService;

    public function __construct(
        AdmissionService $admissionService,
        ImageService $imageService
    )
    {
        $this->admissionService = $admissionService;
        $this->imageService = $imageService;
    }

    private function statusForAdminForm()
    {
        return [
            Admission::ACTIVE => __('Đã duyệt'),
            Admission::DISABLED => __('Chưa duyệt'),
        ];
    }

    public function index(Request $request)
    {
        $filters = $request->all('sortBy', 'sortDir', 'name', 'admission_level_id', 'admission_area_id', 'author_id');
        $admission = $this->admissionService->list($filters, 50);
        $admissionArea = AdmissionArea::where('status', AdmissionArea::ACTIVE)
            ->get()->pluck('name', 'id');
        $admissionLevel = AdmissionLevel::where('status', AdmissionLevel::ACTIVE)
            ->get()->pluck('name', 'id');

        return view('nqadmin-admission::backend.admission.index', compact('admission', 'admissionArea', 'admissionLevel'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        return view('nqadmin-admission::backend.admission.create', [
            'admission' => new Admission,
            'action' => route('nqadmin::admission.store'),
            'admissionArea' => AdmissionArea::where('status', AdmissionArea::ACTIVE)->pluck('name', 'id')->toArray(),
            'admissionLevel' => AdmissionLevel::where('status', AdmissionLevel::ACTIVE)->pluck('name', 'id')->toArray(),
            'active' => Admission::ACTIVE,
            'backUrl' => route('nqadmin::admission.index'),
        ]);
    }

    public function store(AdmissionRequest $request)
    {
        $this->admissionService->create($request);

        return redirect()->route('nqadmin::admission.index')->with(FlashMessage::returnMessage('create'));
    }

    public function edit(Request $request)
    {
        return view('nqadmin-admission::backend.admission.edit', [
            'admission' => Admission::find($request->id),
            'action' => route('nqadmin::admission.update', ['id' => $request->id]),
            'admissionArea' => AdmissionArea::all()->pluck('name', 'id')->toArray(),
            'admissionLevel' => AdmissionLevel::all()->pluck('name', 'id')->toArray(),
            'active' => Admission::ACTIVE,
            'backUrl' => route('nqadmin::admission.index'),
            'preUrl' => $request->preUrl ?? url()->previous(),
        ]);
    }

    public function update(AdmissionRequest $request)
    {
        $this->admissionService->update($request);

        return redirect()->route('nqadmin::admission.edit', [
            'id' => $request->id,
            'preUrl' => $request->preUrl ?? '',
        ])->with(FlashMessage::returnMessage('edit'));
    }

    public function destroy(Request $request)
    {
        $this->admissionService->delete($request);

        return redirect()->route('nqadmin::admission.index')->with(FlashMessage::returnMessage('delete'));
    }

    public function changeStatus(Request $request)
    {
        $inputs = $request->all('status');

        Admission::where('id', $request->id)->update(['status' => $inputs['status'] == 'true' ? Admission::ACTIVE : Admission::DISABLED]);

        return response()->json(['change_status' => true], 200);
    }

    private function validationRuleImage()
    {
        return [
            'image' => ['required', 'mimes:' . config('image.types'), 'max:' . config('image.max_size')],
        ];
    }

    public function uploadImage(Request $request)
    {
        $inputs = [];
        try {
            $inputs = $request->validate($this->validationRuleImage());
        } catch (ValidationException $e) {
            return response()->json([
                'uploaded' => false,
                'error' => [
                    'message' => $e->errors()['image'][0],
                ],
            ]);
        }

        $prefixFolder = 'admission';
        $uploadedPath = $this->imageService->upload($prefixFolder, $inputs['image']);

        return response()->json([
            'uploaded' => true,
            'path' => $uploadedPath,
            'url' => asset($uploadedPath),
        ]);
    }
}
