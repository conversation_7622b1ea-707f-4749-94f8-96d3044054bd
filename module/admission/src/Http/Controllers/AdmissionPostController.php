<?php

namespace Admission\Http\Controllers;

use App\Http\Controllers\Controller as BaseController;
use Admission\Models\Admission;
use Admission\Models\AdmissionPost;
use ClassLevel\Models\ClassLevel;
use Subject\Models\Subject;
use Admission\Services\AdmissionPostService;
use Admission\Services\AdmissionService;
use Base\Services\File\FileService;
use Illuminate\Http\Request;
use Admission\Http\Requests\AdmissionPostRequest;
use Base\Supports\FlashMessage;
use Base\Services\Image\ImageService;
use Illuminate\Validation\ValidationException;

class AdmissionPostController extends BaseController
{
    protected $admissionPostService;
    protected $admissionService;
    protected $imageService;

    public function __construct(
        AdmissionPostService $admissionPostService,
        AdmissionService $admissionService,
        ImageService $imageService
    )
    {
        $this->admissionPostService = $admissionPostService;
        $this->admissionService = $admissionService;
        $this->imageService = $imageService;
    }

    public function index(Request $request)
    {
        $filters = $request->all('sortBy', 'sortDir', 'name', 'type', 'admission_id', 'author_id');
        $admissionPost = $this->admissionPostService->list($filters, 50);
        $admission = Admission::find($request->admission);

        return view('nqadmin-admission::backend.admission_post.index', [
            'admission' => $admission ?: new AdmissionPost(),
            'admissionPost' => $admissionPost,
            'preUrl' => $request->preUrl ?? '',
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $admission = Admission::find($request->admission) ?: new AdmissionPost();
        $action = route('nqadmin::admission_post.store', ['admission' => $admission->id, 'type' => $request->type]);
        $active = AdmissionPost::ACTIVE;
        $backUrl = route('nqadmin::admission_post.index', ['admission' => $admission->id, 'type' => $request->type]);
        $isNew = true;
        $admissionPost = new AdmissionPost();
        $subjects = Subject::all()->pluck('name', 'id')->toArray();
        $classes = ClassLevel::all()->pluck('name', 'id')->toArray();

        return view('nqadmin-admission::backend.admission_post.create', compact(
            'admission',
            'action',
            'active',
            'admissionPost',
            'subjects',
            'classes',
            'backUrl',
            'isNew'
        ));
    }

    public function store(AdmissionPostRequest $request)
    {
        $admissionPost = $this->admissionPostService->create($request);

        return redirect()->route('nqadmin::admission_post.index', ['admission' => $request->admission, 'type' => $admissionPost->type])
            ->with(FlashMessage::returnMessage('create'));
    }

    public function edit(Request $request)
    {
        $active = AdmissionPost::ACTIVE;
        $admissionPost = AdmissionPost::find($request->id);
        $admission = Admission::find($admissionPost->admission_id) ?: new Admission();
        $action = route('nqadmin::admission_post.update', ['id' => $admissionPost->id, 'type' => $admissionPost->type]);
        $backUrl = route('nqadmin::admission_post.index', ['admission' => $admission->id, 'type' => $admissionPost->type]);
        $isNew = false;

        return view('nqadmin-admission::backend.admission_post.edit', [
            'admission' => $admission,
            'action' => $action,
            'active' => $active,
            'admissionPost' => $admissionPost,
            'subjects' => Subject::all()->pluck('name', 'id')->toArray(),
            'classes' => ClassLevel::all()->pluck('name', 'id')->toArray(),
            'isNew' => $isNew,
            'backUrl' => $backUrl,
            'preUrl' => $request->preUrl ?? null,
        ]);
    }

    public function update($id, AdmissionPostRequest $request)
    {
        $admissionPost = $this->admissionPostService->update($id, $request);

        return redirect()->route('nqadmin::admission_post.index', [
                'admission' => $admissionPost->admission_id,
                'preUrl' => $request->preUrl ?? null,
            ])->with(FlashMessage::returnMessage('update'));
    }

    public function destroy(Request $request)
    {
        $this->admissionPostService->delete($request);

        return redirect()->route('nqadmin::admission_post.index', ['admission' => $request->admission])
            ->with(FlashMessage::returnMessage('delete'));
    }

    public function changeStatus(Request $request)
    {
        $inputs = $request->all('status');

        AdmissionPost::where('id', $request->id)
            ->update(['status' => $inputs['status'] == 'true' ? AdmissionPost::ACTIVE : AdmissionPost::DISABLED]);

        return response()->json(['change_status' => true], 200);
    }

    private function validationRuleImage()
    {
        return [
            'image' => ['required', 'mimes:' . config('image.types'), 'max:' . config('image.max_size')],
        ];
    }

    public function uploadImage(Request $request)
    {
        $inputs = [];
        try {
            $inputs = $request->validate($this->validationRuleImage());
        } catch (ValidationException $e) {
            return response()->json([
                'uploaded' => false,
                'error' => [
                    'message' => $e->errors()['image'][0],
                ],
            ]);
        }

        $prefixFolder = 'admission_post';
        $uploadedPath = $this->imageService->upload($prefixFolder, $inputs['image']);

        return response()->json([
            'uploaded' => true,
            'path' => $uploadedPath,
            'url' => asset($uploadedPath),
        ]);
    }
}
