<?php

return [
    'storage_disk' => env('IMAGE_STORAGE_DISK', 'images'),
    'base_folder' => 'upload/images/',
    'max_size' => 20000, // KB
    'types' => 'jpg,png,jpeg,svg,webp,gif',
    'course' => [
        'banner' => [
            'sizes' => [
                // thumnail
                [230, 120],
            ],
        ],
        'livestream' => [
            'sizes' => [
                // thumnail
                [650, 350],
                [230, 120],
            ],
        ],
    ],
    'user' => [
        'avatar' => [
            'sizes' => [
                // thumnail
                [40, 40],
                [120, 120]
            ],
        ],
    ],
    'number_page_to_image' => 5,
    'resolution_doc' => [
        'banner' => 30,
        'content' => 250, // 144 || 300
    ],
];
